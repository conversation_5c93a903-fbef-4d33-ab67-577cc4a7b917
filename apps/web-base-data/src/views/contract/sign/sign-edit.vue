<script setup lang="ts">
import type { ContractInfo } from '#/api';

import { computed, nextTick, ref } from 'vue';

import { OnlyOffice } from '@vben/base-ui';
import { ApiComponent } from '@vben/common-ui';
import { BASE_PAGE_CLASS_NAME, COL_SPAN_PROP, FORM_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';

import { message, Select } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import { BaseFilePickList } from '#/adapter/base-ui';
import {
  copyFileApi,
  getContractClassifyListApi,
  getContractSignInfoApi,
  getContractTemplateInfoApi,
  getOnlyOfficeFileInfoApi,
  saveContractSignApi,
  saveOnlyOfficeFileApi,
} from '#/api';

const emit = defineEmits(['ok', 'register']);
const dictStore = useDictStore();
const init = async (data: ContractInfo) => {
  if (data.id) {
    contractForm.value = await getContractSignInfoApi({ id: data.id });
  } else {
    contractForm.value = { ...data };
    if (contractForm.value.createType === '2' && contractForm.value.templateId) {
      const templateInfo = await getContractTemplateInfoApi({ id: contractForm.value.templateId });
      contractForm.value.categoryId = templateInfo.categoryId;
      const res = await copyFileApi({ id: templateInfo.fileId });
      contractForm.value.fileId = res.id;
    }
  }
  // await gridApi.grid.reloadData(contractForm.value.signDetailList ?? []);
  await nextTick();
  EditorRef.value.init(contractForm.value.fileId, 'edit');
};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const modalTitle = ref('新增合同');
const ContractFormRef = ref();
const EditorRef = ref();
const contractForm = ref<ContractInfo>({});
const rules = {
  contractName: [{ required: true, message: '请输入合同名称' }],
  contractCode: [{ required: true, message: '请输入合同编码' }],
  signMethod: [{ required: true, message: '请选择签约方式', trigger: 'change' }],
};
const colSpanProp = COL_SPAN_PROP;
const save = async () => {
  await ContractFormRef.value.validate();
  // const errMap = await gridApi.grid.validate(true);
  // if (errMap) {
  //   const errMessage = getCombinedErrorMessagesString(errMap);
  //   if (errMessage) {
  //     message.error(errMessage);
  //     return;
  //   }
  // }
  changeOkLoading(true);
  try {
    const formData = cloneDeep(contractForm.value);
    // const { visibleData } = gridApi.grid.getTableData();
    // formData.signDetailList = visibleData;
    const res = await saveContractSignApi(formData);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    changeOkLoading(false);
  }
};
// const baseGridOptions = {
//   showOverflow: 'title',
//   keepSource: true,
//   editConfig: {
//     trigger: 'click',
//     mode: 'row',
//     autoClear: false,
//   },
//   rowConfig: {
//     drag: true,
//   },
//   pagerConfig: {
//     enabled: false,
//   },
//   toolbarConfig: {
//     slots: {
//       tools: 'toolbarTools',
//     },
//     custom: false,
//     refresh: false,
//     resizable: false,
//     zoom: false,
//   },
// };
// const signGridOptions = {
//   columns: [
//     { type: 'checkbox', width: '60px', fixed: 'left' },
//     {
//       field: 'signerType',
//       title: '类型',
//       minWidth: '100px',
//       formatter: ['formatStatus', 'SIGN_USER_TYPE'],
//     },
//     {
//       field: 'signatoryParam',
//       title: '签约角色',
//       slots: { default: 'edit_signatory_param' },
//       minWidth: '160px',
//     },
//     {
//       field: 'signInfo',
//       title: '签约方信息',
//       slots: { default: 'edit_sign_info' },
//       minWidth: '500px',
//     },
//   ],
//   editRules: {
//     signatoryParam: [{ required: true, content: '请输入签约角色' }],
//     signInfo: [
//       {
//         validator({ row }) {
//           if (row.signerType === '1' && !row.signatoryOrg) {
//             return new Error('请输入签约企业');
//           }
//           if (row.signerType === '0' && !row.signatoryName) {
//             return new Error('请输入真实姓名');
//           }
//           // if (!row.signatoryAccount) {
//           //   return new Error('请输入手机号或邮箱');
//           // }
//           return true;
//         },
//       },
//     ],
//   },
//   ...baseGridOptions,
// } as VxeTableGridOptions;
// const [Grid, gridApi] = useVbenVxeGrid({
//   gridOptions: signGridOptions,
// });
// const addSignatory = (type: string) => {
//   gridApi.grid.insertAt({
//     signerType: type,
//   });
// };
// const removeSignatory = () => {
//   const records = gridApi.grid.getCheckboxRecords();
//   gridApi.grid.remove(records);
// };
const saveDoc = async () => {
  await EditorRef.value.save();
};
const sealType = computed({
  get() {
    return contractForm.value.sealType ? contractForm.value.sealType.split(',') : [];
  },
  set(value: string[]) {
    contractForm.value.sealType = value ? value.join(',') : undefined;
  },
});
const initOnlyOffice = async () => {
  await EditorRef.value.init(contractForm.value.fileId, 'edit');
};
defineExpose({
  init,
});
</script>

<template>
  <BasicPopup :title="modalTitle" show-ok-btn @register="registerPopup" @ok="save">
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-form ref="ContractFormRef" class="" :model="contractForm" :rules="rules" v-bind="FORM_PROP">
        <a-row class="mt-5">
          <a-col v-bind="colSpanProp">
            <a-form-item label="合同名称" name="contractName">
              <a-input v-model:value="contractForm.contractName" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpanProp">
            <a-form-item label="合同编码" name="contractCode">
              <a-input v-model:value="contractForm.contractCode" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpanProp">
            <a-form-item label="业务类型" name="businessType">
              <a-select
                v-model:value="contractForm.businessType"
                :options="dictStore.getDictList('BUSINESS_TYPE')"
                :disabled="!!contractForm.id"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpanProp">
            <a-form-item label="业务编码" name="businessCode">
              <a-select v-model:value="contractForm.businessCode" :disabled="!!contractForm.id" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpanProp">
            <a-form-item label="用章类型" name="sealType">
              <a-select v-model:value="sealType" :options="dictStore.getDictList('SEAL_TYPE')" mode="multiple" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpanProp">
            <a-form-item label="合同分类" name="categoryId">
              <ApiComponent
                :component="Select"
                v-model="contractForm.categoryId as unknown as string"
                :api="getContractClassifyListApi"
                label-field="categoryName"
                value-field="id"
                model-prop-name="value"
              />
            </a-form-item>
          </a-col>
          <!--<a-col v-bind="colSpanProp">-->
          <!--  <a-form-item label="签约类型" name="signMethod">-->
          <!--    <a-select v-model:value="contractForm.signMethod" :options="dictStore.getDictList('baseEnableType')" />-->
          <!--  </a-form-item>-->
          <!--</a-col>-->
        </a-row>
        <!--<BasicCaption content="设置签约方" />-->
        <!--<Grid>-->
        <!--  <template #toolbarTools>-->
        <!--    <a-space>-->
        <!--      <a-button type="primary" @click="addSignatory('0')">添加签约个人</a-button>-->
        <!--      <a-button type="primary" @click="addSignatory('1')">添加签约企业</a-button>-->
        <!--      <a-button type="primary" danger @click="removeSignatory">删除</a-button>-->
        <!--    </a-space>-->
        <!--  </template>-->
        <!--  <template #edit_signatory_param="{ row }">-->
        <!--    <AutoComplete v-model:value="row.signatoryParam" class="w-full" />-->
        <!--  </template>-->
        <!--  <template #edit_sign_info="{ row }">-->
        <!--    <a-space>-->
        <!--      <a-input v-if="row.signerType === '1'" v-model:value="row.signatoryOrg" placeholder="请输入签约企业" />-->
        <!--      <a-input v-model:value="row.signatoryName" placeholder="请输入真实姓名" />-->
        <!--      <a-input v-model:value="row.signatoryAccount" placeholder="请输入手机号或邮箱" />-->
        <!--    </a-space>-->
        <!--  </template>-->
        <!--  <template #sign_info="{ row }">-->
        <!--    <a-space>-->
        <!--      <span v-if="row.signerType === '1'">{{ row.signatoryOrg }}</span>-->
        <!--      <span>{{ row.signatoryName }}</span>-->
        <!--      <span>{{ row.signatoryAccount }}</span>-->
        <!--    </a-space>-->
        <!--  </template>-->
        <!--</Grid>-->
        <BasicCaption content="已签署合同" />
        <a-row class="mt-5">
          <a-col v-bind="colSpanProp">
            <a-form-item label="合同文件" name="fileCompleteId">
              <BaseFilePickList v-model="contractForm.fileCompleteId" />
            </a-form-item>
          </a-col>
        </a-row>
        <BasicCaption content="未签署合同">
          <template v-if="contractForm.fileId" #action>
            <a-button type="primary" @click="saveDoc">保存</a-button>
          </template>
        </BasicCaption>
        <a-row v-if="contractForm.createType === '1'" class="mt-5">
          <a-col v-bind="colSpanProp">
            <a-form-item label="合同文件" name="fileId">
              <BaseFilePickList v-model="contractForm.fileId" @pick="initOnlyOffice" />
            </a-form-item>
          </a-col>
        </a-row>
        <template v-if="contractForm.fileId">
          <div class="h-[600px]">
            <OnlyOffice ref="EditorRef" :get-config-api="getOnlyOfficeFileInfoApi" :save-api="saveOnlyOfficeFileApi" />
          </div>
        </template>
      </a-form>
    </div>
  </BasicPopup>
</template>

<style></style>
