<script setup lang="ts">
import type { ContractInfo } from '#/api';

import { ref } from 'vue';

import { StatusTag } from '@vben/base-ui';
import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';

import { BaseFileList } from '#/adapter/base-ui';
import { getContractSignInfoApi } from '#/api';

defineEmits(['register']);
const contractForm = ref<ContractInfo>({});
const init = async (data: ContractInfo) => {
  contractForm.value = data.id
    ? await getContractSignInfoApi({ id: data.id })
    : {
        ...data,
      };
  await gridApi.grid.reloadData(contractForm.value.signDetailList ?? []);
};
const [registerPopup] = usePopupInner(init);
</script>

<template>
  <BasicPopup title="合同详情" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME">
      <BasicCaption :content="`${contractForm.contractName ?? ''}（${contractForm.contractCode ?? ''}）`" />
      <a-descriptions v-bind="DESCRIPTIONS_PROP" class="mt-4">
        <a-descriptions-item label="业务类型">
          <StatusTag code="CONTRACT_BUSINESS_TYPE" :value="contractForm.businessType" />
        </a-descriptions-item>
        <a-descriptions-item label="业务编码">
          {{ contractForm.businessCode }}
        </a-descriptions-item>
        <a-descriptions-item label="合同分类">
          {{ contractForm.categoryName }}
        </a-descriptions-item>
        <a-descriptions-item label="用章类型">
          <StatusTag code="SEAL_TYPE" :value="contractForm.sealType" />
        </a-descriptions-item>
        <a-descriptions-item label="未签署合同">
          <BaseFileList :model-value="contractForm.fileId" />
        </a-descriptions-item>
        <a-descriptions-item label="已签署合同">
          <BaseFileList :model-value="contractForm.fileCompleteId" />
        </a-descriptions-item>
      </a-descriptions>
    </div>
  </BasicPopup>
</template>

<style></style>
