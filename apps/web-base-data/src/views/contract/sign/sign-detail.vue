<script setup lang="ts">
// import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { ContractInfo } from '#/api';

import { ref } from 'vue';

import { StatusTag } from '@vben/base-ui';
import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
// import { useVbenVxeGrid } from '@vben/plugins/vxe-table';

import { BaseFileList } from '#/adapter/base-ui';
import { getContractSignInfoApi } from '#/api';

defineEmits(['register']);
const contractForm = ref<ContractInfo>({});
const init = async (data: ContractInfo) => {
  contractForm.value = data.id
    ? await getContractSignInfoApi({ id: data.id })
    : {
        ...data,
      };
  // await gridApi.grid.reloadData(contractForm.value.signDetailList ?? []);
};
const [registerPopup] = usePopupInner(init);
// const signGridOptions = {
//   columns: [
//     {
//       field: 'signerType',
//       title: '类型',
//       minWidth: '100px',
//       formatter: ['formatStatus', 'SIGN_USER_TYPE'],
//     },
//     {
//       field: 'signatoryParam',
//       title: '签约角色',
//       minWidth: '160px',
//     },
//     {
//       field: 'signInfo',
//       title: '签约方信息',
//       slots: { default: 'sign_info' },
//       minWidth: '500px',
//     },
//   ],
//   ...DETAIL_GRID_OPTIONS,
// } as VxeTableGridOptions;
// const [Grid, gridApi] = useVbenVxeGrid({
//   gridOptions: signGridOptions,
// });
</script>

<template>
  <BasicPopup title="合同详情" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions v-bind="DESCRIPTIONS_PROP" class="mt-4">
        <a-descriptions-item label="合同名称">
          {{ contractForm.contractName }}
        </a-descriptions-item>
        <a-descriptions-item label="合同编号">
          {{ contractForm.contractCode }}
        </a-descriptions-item>
        <a-descriptions-item label="业务类型">
          <StatusTag code="CONTRACT_BUSINESS_TYPE" :value="contractForm.businessType" />
        </a-descriptions-item>
        <a-descriptions-item label="业务编码">
          {{ contractForm.businessCode }}
        </a-descriptions-item>
        <a-descriptions-item label="用章类型">
          <StatusTag code="SEAL_TYPE" :value="contractForm.sealType" />
        </a-descriptions-item>
        <a-descriptions-item label="合同分类">
          {{ contractForm.categoryName }}
        </a-descriptions-item>
      </a-descriptions>
      <!--<BasicCaption content="签约方" />-->
      <!--<Grid>-->
      <!--  <template #sign_info="{ row }">-->
      <!--    <a-space>-->
      <!--      <span v-if="row.signerType === '1'">{{ row.signatoryOrg }}</span>-->
      <!--      <span>{{ row.signatoryName }}</span>-->
      <!--      <span>{{ row.signatoryAccount }}</span>-->
      <!--    </a-space>-->
      <!--  </template>-->
      <!--</Grid>-->
      <BasicCaption content="合同文件" />
      <a-descriptions v-bind="DESCRIPTIONS_PROP" class="mt-4">
        <a-descriptions-item label="未签署合同">
          <BaseFileList :model-value="contractForm.fileId" />
        </a-descriptions-item>
        <a-descriptions-item label="已签署合同">
          <BaseFileList :model-value="contractForm.fileCompleteId" />
        </a-descriptions-item>
      </a-descriptions>
    </div>
  </BasicPopup>
</template>

<style></style>
