<script setup lang="ts">
import type { CompanyInfo, CompanyMortgageBO, CompanyShareholderBO } from '#/api';

import { ref } from 'vue';

import { BASE_PAGE_CLASS_NAME } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';

import { message } from 'ant-design-vue';
import BigNumber from 'bignumber.js';
import dayjs from 'dayjs';
import { cloneDeep, merge } from 'lodash-es';

import {
  addCompanyApi,
  editCompanyApi,
  getCompanyInfoApi,
  getRegionInfoApi,
  searchCompanyInfoApi,
  searchCompanyMortgageApi,
  searchCompanyPartnerApi,
} from '#/api';
import BaseForm from '#/views/company/manage/components/base-form.vue';

const emit = defineEmits(['ok', 'register']);
const companyForm = ref<CompanyInfo>({
  companyCode: '',
  companyName: '',
  province: '',
  city: '',
  district: '',
  districtCode: '',
});
// const state = reactive({
//   activeKey: 'base',
// });
const init = async (data: CompanyInfo) => {
  if (data?.id) {
    const res = await getCompanyInfoApi({ id: data.id as number });
    if (res.shareholderList) {
      res.shareholderList.forEach((item: CompanyShareholderBO) => {
        if (item.startDate) {
          item.startDate = Number(dayjs(item.startDate).format('x'));
        }
      });
    }
    if (res.mortgageList) {
      res.mortgageList.forEach((item: CompanyMortgageBO) => {
        item.regDate && (item.regDate = Number(dayjs(item.regDate).format('x')));
        item.startDate && (item.startDate = Number(dayjs(item.startDate).format('x')));
        item.endDate && (item.endDate = Number(dayjs(item.endDate).format('x')));
      });
    }
    companyForm.value = res;
    BaseFormRef.value.init(companyForm.value);
  } else {
    if (data.keyword) {
      await getCompanySearchData(data.keyword);
      await getCompanyPartner(data.keyword);
      await getCompanyMortgage(data.keyword);
      BaseFormRef.value.init(companyForm.value);
    }
  }
};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const BaseFormRef = ref();
const save = async () => {
  await BaseFormRef.value.save();
  const formData = cloneDeep(companyForm.value);
  changeOkLoading(true);
  let api = addCompanyApi;
  if (formData.id) {
    api = editCompanyApi;
  }
  try {
    const res = await api(formData);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    changeOkLoading(false);
  }
};
const close = () => {
  companyForm.value = {
    companyCode: '',
    companyName: '',
    province: '',
    city: '',
    district: '',
    districtCode: '',
  };
};
const getCompanySearchData = async (keyword?: string) => {
  if (!keyword) return;
  const { data: res } = await searchCompanyInfoApi({ keyword });
  if (res) {
    companyForm.value = merge(companyForm.value, {
      companyName: res.name,
      companyCode: res.creditNo,
      regCapital: res.registCapi,
      actualCapital: res.actualCapi,
      establishTime: Number(dayjs(res.startDate).format('x')),
      regStatus: res.new_status,
      regNumber: res.regNo,
      orgNumber: res.orgNo,
      companyOrgType: res.econKind,
      fromTime: Number(dayjs(res.termStart).format('x')),
      toTime: Number(dayjs(res.termEnd).format('x')),
      regLocation: res.address,
      bizLocation: res.address,
      bizScope: res.scope,
      legalPersonName: res.operName,
      industry: res.domain,
      districtCode: res.districtCode,
    });
    if (res.districtCode) {
      const regionRes = await getRegionInfoApi({ id: res.districtCode });
      companyForm.value.province = regionRes.province;
      companyForm.value.city = regionRes.city;
      companyForm.value.district = regionRes.district;
    }
  }
};
const getCompanyPartner = async (keyword?: string) => {
  if (!keyword) return;
  const { data: res = [] } = await searchCompanyPartnerApi({ keyword });
  const shareholderList: CompanyShareholderBO[] = [];
  res.forEach((item: CompanyShareholderBO) => {
    shareholderList.push({
      name: item.name,
      shouldCapital: item.totalShouldCapi,
      realCapital: item.totalRealCapi,
      stockPercent: new BigNumber(item.stockPercent ?? 0).times(100).toFixed(2),
      startDate: Number(dayjs(item.subConamDate).format('x')),
    });
  });
  shareholderList.sort((a, b) => b.stockPercent - a.stockPercent);
  companyForm.value.shareholderList = shareholderList;
};
const getCompanyMortgage = async (keyword?: string) => {
  if (!keyword) return;
  const { data: res = [] } = await searchCompanyMortgageApi({ keyword });
  const mortgageList: CompanyMortgageBO[] = [];
  res.forEach((item: CompanyMortgageBO) => {
    mortgageList.push({
      regDate: Number(dayjs(item.date).format('x')),
      number: item.number,
      mortgagee: item.mortgagees?.map((mortgagee: any) => mortgagee.name).join(','),
      debitType: item.debit_type,
      amount: item.debit_amount,
      department: item.department,
      startDate: Number(dayjs(item.period_start).format('x')),
      endDate: Number(dayjs(item.period_end).format('x')),
    });
  });
  companyForm.value.mortgageList = mortgageList;
};
</script>

<template>
  <BasicPopup
    v-bind="$attrs"
    show-ok-btn
    title="企业信息"
    destroy-on-close
    @register="registerPopup"
    @ok="save"
    @close="close"
  >
    <div :class="BASE_PAGE_CLASS_NAME">
      <BaseForm
        ref="BaseFormRef"
        v-model="companyForm"
        :sync-main-info="getCompanySearchData"
        :sync-shareholder-info="getCompanyPartner"
        :sync-mortgage-info="getCompanyMortgage"
      />
    </div>
  </BasicPopup>
</template>

<style></style>
