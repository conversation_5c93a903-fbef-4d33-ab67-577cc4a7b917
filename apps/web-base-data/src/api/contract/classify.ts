import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

/**
 * 合同分类表
 */
export interface ContractClassifyInfo {
  // 编码
  categoryCode?: string;
  // 分类名称
  categoryName?: string;
  // 标记删除，已删除：1，未删除：0
  deleteFlag?: number;
  // 主键
  id?: number;
  // 业务描述
  remark?: string;
  // 版本号
  version?: number;
  [property: string]: any;
}

export async function getContractClassifyPageListApi(params: PageListParams) {
  return requestClient.get('/base/contract/category/pageList', { params });
}
export async function getContractClassifyListApi() {
  return requestClient.get('/base/contract/category/list');
}
export async function addContractClassifyApi(data: ContractClassifyInfo) {
  return requestClient.post('/base/contract/category/create', data);
}
export async function editContractClassifyApi(data: ContractClassifyInfo) {
  return requestClient.post('/base/contract/category/update', data);
}
export async function deleteContractClassifyApi(id: number) {
  return requestClient.post('/base/contract/category/delete', {}, { params: { id } });
}
