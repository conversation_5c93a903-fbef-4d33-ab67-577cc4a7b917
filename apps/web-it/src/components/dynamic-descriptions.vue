<script setup lang="ts">
import { StatusTag } from '@vben/base-ui';
import { getNestedValue } from '@vben/utils';

import { Descriptions, DescriptionsItem } from 'ant-design-vue';

interface Props {
  schema: {
    dictCode?: string;
    fieldName: string;
    formatter?: Function;
    label: string;
    layoutType?: string;
    slotName?: string;
    span?: number;
  }[];
  data: object;
}

const props = defineProps<Props>();
const getValue = (item: Props['schema'][number]) => {
  return item.formatter
    ? item.formatter(getNestedValue(props.data, item.fieldName))
    : getNestedValue(props.data, item.fieldName);
};
const handleShow = (item: Props['schema'][number]) => {
  const checkShow = getNestedValue(item, 'dependencies.show');
  return checkShow ? checkShow(props.data) : true;
};
</script>

<template>
  <Descriptions v-bind="$attrs">
    <template v-for="(item, index) in props.schema" :key="item.fieldName + index">
      <DescriptionsItem v-if="handleShow(item)" :label="item.label" :span="item.span">
        <slot :name="item.slotName" v-bind="{ data: getValue(item) }">
          <StatusTag v-if="item.layoutType === 'tag'" :value="getValue(item)" :code="item.dictCode" />
          <template v-else>
            {{ getValue(item) }}
          </template>
        </slot>
      </DescriptionsItem>
    </template>
  </Descriptions>
</template>

<style></style>
