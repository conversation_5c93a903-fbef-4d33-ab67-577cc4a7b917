import type { PageListParams, Pagination } from '@vben/types';

import { requestClient } from '#/api/request';

/**
 * 权限授权请求对象
 */
export interface PermissionGroupInfo {
  // 编码
  code?: string;
  // 描述
  description?: string;
  // 是否启用
  enabled?: number;
  // 主键
  id?: number;
  // 名称
  name?: string;
  // 权限分组成员列表
  permissionMemberList?: PermissionMemberBO[];
  // 排序
  sortCode?: number;
  // 租户主键
  tenantId?: string;
  [property: string]: any;
}

/**
 * PermissionMemberBO，权限授权业务对象
 */
export interface PermissionMemberBO {
  // 对象描述
  description?: string;
  // 主键
  id?: number;
  // 对象名称
  name?: string;
  // 对象ID
  objectId: number;
  // 对象类型（枚举值）
  objectType: string;
  // 权限组Id
  permissionId?: number;
  // 租户主键
  tenantId?: string;
  [property: string]: any;
}

export interface PermissionAppVO {
  // 应用编码
  code?: string;
  // 描述
  description?: string;
  // 是否启用
  enabled?: number;
  // 图标
  icon?: string;
  // 主键
  id?: number;
  // 是否主系统
  isMain?: number;
  // 模块列表
  moduleList?: ModuleVO[];
  // 应用名称
  name?: string;
  // 排序
  sortCode?: number;
  // 应用类型:PLATFORM=平台应用,CUSTOMER=客户应用
  type?: string;
  // 应用地址
  url?: string;
  [property: string]: any;
}

/**
 * ModuleVO，模块列表
 */
interface ModuleVO {
  // 系统CODE
  appCode?: string;
  // 模块列表
  buttonList?: ModuleButtonVO[];
  // 分类
  category?: string;
  children?: ModuleVO[];
  // 编码
  code?: string;
  // 组件
  component?: string;
  // 描述
  description?: string;
  // 有效标志(0-禁用，1-启用)
  enabled?: number;
  // 菜单图标
  icon?: string;
  // 主键
  id?: number;
  // 按钮权限
  isButtonAuth?: number;
  // 列表权限
  isColumnAuth?: number;
  // 数据权限
  isDataAuth?: number;
  // 表单权限
  isFormAuth?: number;
  isLeaf?: boolean;
  // 层级
  level?: number;
  // 链接目标
  linkTarget?: LinkTarget;
  // 关联功能id
  moduleId?: number;
  // 名称
  name?: string;
  // 上级id
  parentId?: number;
  // 上级路径
  parentPath?: string;
  // 扩展属性
  propertyJson?: string;
  // 排序
  sortCode?: number;
  // 类型(1-目录 2-页面 3-外链)
  type?: string;
  // 路由地址
  urlAddress?: string;
  [property: string]: any;
}

/**
 * ModuleButtonVO，模块列表
 */
interface ModuleButtonVO {
  // 编码
  code?: string;
  // 描述
  description?: string;
  // 有效标志(0-禁用，1-启用)
  enabled?: number;
  // 主键
  id?: number;
  // 关联功能id
  moduleId?: number;
  // 名称
  name?: string;
  // 扩展属性
  propertyJson?: string;
  // 排序
  sortCode?: number;
  [property: string]: any;
}

/**
 * 链接目标
 */
export enum LinkTarget {
  // 新窗口打开
  Blank = '_blank',
  // 当前窗口打开
  Self = '_self',
}

export function getPermissionGroupPageListApi(params: PageListParams) {
  return requestClient.get<Pagination<PermissionGroupInfo>>('/upms/permission/group/page', { params });
}
export function addPermissionGroupApi(data: PermissionGroupInfo) {
  return requestClient.post('/upms/permission/group/add', data);
}
export function editPermissionGroupApi(data: PermissionGroupInfo) {
  return requestClient.post('/upms/permission/group/edit', data);
}
export function savePermissionGroupMemberApi(
  params: { id: number },
  data: { objectId: number; objectType: 'GROUP' | 'POST' | 'ROLE' | 'USER' }[],
) {
  return requestClient.post('/upms/permission/group/members', data, { params });
}
export function getPermissionGroupDetailApi(params: { id: number }) {
  return requestClient.get<PermissionGroupInfo>('/upms/permission/group/detail', { params });
}
export function savePermissionAuthApi(
  params: { id: number },
  data: { appIdList: number[]; buttonIdList: number[]; moduleIdList: number[] },
) {
  return requestClient.post('/upms/permission/authorize/save-data', data, { params });
}
export function getPermissionAuthApi(params: { id: number }) {
  return requestClient.get<{ appIdList: number[]; buttonIdList: number[]; moduleIdList: number[] }>(
    '/upms/permission/authorize/get-data',
    { params },
  );
}
export function getPermissionAuthTreeApi(params: { id: number }) {
  return requestClient.get('/upms/permission/authorize/get-tree', { params });
}
export function deletePermissionGroupApi(params: { id: number }) {
  return requestClient.post('/upms/permission/group/delete', {}, { params });
}
export function getPermissionAuthAllTreeApi() {
  return requestClient.get<PermissionAppVO[]>('/upms/permission/authorize/get-all-tree');
}
