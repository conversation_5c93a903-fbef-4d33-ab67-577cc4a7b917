<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { ProjectBaseInfo, ProjectTransferInfo } from '#/api';

import { onMounted, reactive, ref } from 'vue';

import { Page } from '@vben/common-ui';
import { FeUserSelect, usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import {
  Modal as AntdModal,
  Button,
  Form,
  FormItem,
  message,
  Modal,
  Space,
  Spin,
  TypographyLink,
} from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  getCompanyApi,
  getUserInfoByIdsApi,
  getUserListApi,
  getUserListByKeywordApi,
  getUserTreeListApi,
  projectChangePageListApi,
  projectManageCompleteApi,
  projectManagePageApi,
  projectManageTransferApi,
} from '#/api';

import Detail from '../components/overview.vue';
import Change from './change.vue';

const { getDictList } = useDictStore();
const labelCol = { style: { width: '150px' } };
const sortKey = ref<string>('create_time');
const dataLoaded = ref(false); // 添加加载状态
const usersOptions = ref([]);
const companyOptions = ref([]);
// 获取用户列表
const getUserList = async () => {
  const res = await getUserListApi();
  Object.assign(usersOptions.value, res);
};
// 获取企业列表
const getCompanyList = async () => {
  const res = await getCompanyApi();
  Object.assign(companyOptions.value, res);
};
// 同时执行两个异步请求
const loadData = async () => {
  try {
    await Promise.all([getCompanyList(), getUserList()]);
  } finally {
    dataLoaded.value = true;
  }
};

const transferForm = reactive<ProjectTransferInfo>({
  id: undefined,
  businessManagerId: [],
  operationManagerId: [],
  financeManagerId: [],
  riskManagerId: [],
});

// 根据接口必填字段定义验证规则
const rules: Record<string, Rule[]> = {
  businessManagerId: [{ required: true, message: '请选择业务负责人', trigger: 'change', type: 'array' }],
  operationManagerId: [{ required: true, message: '请选择经营负责人', trigger: 'change', type: 'array' }],
  financeManagerId: [{ required: true, message: '请选择财务负责人', trigger: 'change', type: 'array' }],
  riskManagerId: [{ required: true, message: '请选择风控负责人', trigger: 'change', type: 'array' }],
};

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'projectCode',
      label: '项目编号',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '项目名称',
    },
    {
      component: 'Select',
      fieldName: 'businessStructure',
      label: '业务结构',
      componentProps: {
        options: getDictList('BUS_STRUCTURE'),
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'projectModel',
      label: '项目模式',
      componentProps: {
        options: getDictList('PROJECT_MODE'),
        allowClear: true,
      },
    },
    // {
    //   component: 'Select',
    //   fieldName: 'projectReviewStatus',
    //   label: '评审状态',
    //   componentProps: {
    //     options: getDictList('PROJECT_REVIEW_STATUS'),
    //     allowClear: true,
    //   },
    // },
    {
      component: 'Select',
      fieldName: 'purchaseMode',
      label: '采购模式',
      componentProps: {
        options: getDictList('PURCHASE_MODE'),
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '项目状态',
      componentProps: {
        options: getDictList('PROJECT_STATUS'),
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'changeStatus',
      label: '变更状态',
      componentProps: {
        options: getDictList('PROJECT_REVIEW_STATUS'),
        allowClear: true,
      },
    },
  ],
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
});

const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    { field: 'projectCode', title: '项目编号' },
    { field: 'projectName', title: '项目名称' },
    {
      field: 'businessStructure',
      title: '业务结构',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'BUS_STRUCTURE',
        },
      },
    },
    {
      field: 'projectModel',
      title: '项目模式',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'PROJECT_MODE',
        },
      },
    },
    // { field: 'executorCompanyName', title: '贸易执行企业' },
    {
      field: 'purchaseMode',
      title: '采购模式',
      width: 150,
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'PURCHASE_MODE',
          displayMode: 'collapse',
        },
      },
    },
    // {
    //   field: 'approvalStatus',
    //   title: '审批状态',
    //   cellRender: {
    //     name: 'CellStatus',
    //     props: {
    //       code: 'REVIEW_STATUS',
    //     },
    //   },
    // },
    {
      field: 'status',
      title: '项目状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'PROJECT_STATUS',
        },
      },
    },
    {
      field: 'changeStatus',
      title: '变更状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'PROJECT_REVIEW_STATUS',
        },
      },
    },
    {
      field: 'businessManager',
      title: '业务负责人',
      formatter: (managers) => {
        if (!managers.cellValue || !Array.isArray(managers.cellValue) || managers.cellValue.length === 0) {
          return '';
        }
        return managers.cellValue
          .map((manager) => manager.userName)
          .filter(Boolean)
          .join(', ');
      },
    },
    // { field: 'createName', title: '创建人' },
    { field: 'createTime', title: '创建日期', formatter: 'formatDate' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        const processedFormValues = { ...formValues };
        return await projectManagePageApi({
          current: page.currentPage,
          size: page.pageSize,
          descs: sortKey.value,
          ...processedFormValues,
        });
      },
    },
  },
  checkboxConfig: {
    showHeader: false,
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const changeGridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    { field: 'projectCode', title: '项目编号' },
    { field: 'projectName', title: '项目名称' },
    {
      field: 'businessStructure',
      title: '业务结构',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'BUS_STRUCTURE',
        },
      },
    },
    {
      field: 'projectModel',
      title: '项目模式',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'PROJECT_MODE',
        },
      },
    },
    // { field: 'executorCompanyName', title: '贸易执行企业' },
    {
      field: 'purchaseMode',
      title: '采购模式',
      width: 150,
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'PURCHASE_MODE',
          displayMode: 'collapse',
        },
      },
    },
    // {
    //   field: 'approvalStatus',
    //   title: '审批状态',
    //   cellRender: {
    //     name: 'CellStatus',
    //     props: {
    //       code: 'REVIEW_STATUS',
    //     },
    //   },
    // },
    {
      field: 'changeStatus',
      title: '变更状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'PROJECT_REVIEW_STATUS',
        },
      },
    },
    {
      field: 'businessManager',
      title: '业务负责人',
      formatter: (managers) => {
        if (!managers.cellValue || !Array.isArray(managers.cellValue) || managers.cellValue.length === 0) {
          return '';
        }
        return managers.cellValue
          .map((manager) => manager.userName)
          .filter(Boolean)
          .join(', ');
      },
    },
    // { field: 'createName', title: '创建人' },
    { field: 'createTime', title: '创建日期', formatter: 'formatDate' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        const processedFormValues = { ...formValues };
        return await projectChangePageListApi({
          current: page.currentPage,
          size: page.pageSize,
          descs: sortKey.value,
          ...processedFormValues,
        });
      },
    },
  },
  checkboxConfig: {
    showHeader: false,
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

const manageTransferModal = reactive({
  visible: false,
});

const handleTransferOk = async () => {
  try {
    await projectManageTransferApi(transferForm);
    manageTransferModal.visible = false;
    await gridApi.formApi.submitForm();
    message.success($t('base.resSuccess'));
  } catch (error: any) {
    message.error(error.message || $t('base.resFail'));
  }
};

const handleTransferCancel = () => {
  manageTransferModal.visible = false;
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents: {
    checkboxChange: ({ checked, row }: { checked: boolean; row: ProjectBaseInfo }) => {
      if (checked) {
        gridApi.grid.setAllCheckboxRow(false);
        gridApi.grid.setCheckboxRow(row, true);
      }
    },
  },
});

const [registerDetailForm, { openPopup: openDetailPopup }] = usePopup();
const [registerChangeForm, { openPopup: openChangePopup }] = usePopup();

const projectType = 'info';
const detail = (row: ProjectBaseInfo) => {
  const type = activeKey.value === 'change' ? 'change' : projectType;
  const detailRow = { ...row, projectType: type };
  openDetailPopup(true, detailRow);
};

const change = async (row?: ProjectBaseInfo) => {
  const selectedRecords = gridApi.grid.getCheckboxRecords();
  if (!row && (!selectedRecords || selectedRecords.length === 0)) {
    message.warning('请选择要变更的项目');
    return;
  }
  const data = row || selectedRecords[0];
  if (data.changeStatus !== 'UNDER_REVIEW' && data.status !== 'COMPLETED') {
    openChangePopup(true, data);
  } else {
    message.warning('正在变更中，不能变更');
  }
};

const complete = async (row?: ProjectBaseInfo) => {
  const selectedRecords = gridApi.grid.getCheckboxRecords();
  if (!row && (!selectedRecords || selectedRecords.length === 0)) {
    message.warning('请选择要完成的项目');
    return;
  }
  const data = row || selectedRecords[0];
  if (
    data.status === 'EFFECTIVE' &&
    (data.changeStatus === 'NOT_CHANGE' || data.changeStatus === 'REVIEWED' || data.changeStatus === 'NOT_REVIEWED')
  ) {
    AntdModal.confirm({
      title: $t('base.confirmCompleteTitle'),
      content: $t('base.confirmCompleteContent'),
      async onOk() {
        try {
          const submitRow = {
            id: data.id,
            status: 'COMPLETED',
          };
          await projectManageCompleteApi(submitRow);
          message.success($t('base.resSuccess'));
          await gridApi.formApi.submitForm();
        } catch (error: any) {
          message.error(`失败: ${error.message}`);
        }
      },
    });
  }
};

const cancelComplete = async (row: ProjectBaseInfo) => {
  AntdModal.confirm({
    title: $t('base.confirmCancelCompleteTitle'),
    content: $t('base.confirmCancelCompleteContent'),
    async onOk() {
      try {
        const submitRow = {
          id: row.id,
          status: 'EFFECTIVE',
        };
        await projectManageCompleteApi(submitRow);
        message.success($t('base.resSuccess'));
        await gridApi.formApi.submitForm();
      } catch (error: any) {
        message.error(`失败: ${error.message}`);
      }
    },
  });
};

const detailSuccess = () => {
  gridApi.formApi.submitForm();
};

const changeSuccess = () => {
  gridApi.formApi.submitForm();
};

const transfer = async (row?: ProjectBaseInfo) => {
  const selectedRecords = gridApi.grid.getCheckboxRecords();
  if (!row && (!selectedRecords || selectedRecords.length === 0)) {
    message.warning('请选择要移交的项目');
    return;
  }
  const data = row || selectedRecords[0];
  if (data.changeStatus !== 'UNDER_REVIEW' && data.status !== 'COMPLETED') {
    manageTransferModal.visible = true;
    Object.assign(transferForm, {
      id: data.id,
      businessManagerId: data.businessManagerId ?? [],
      operationManagerId: data.operationManagerId ?? [],
      financeManagerId: data.financeManagerId ?? [],
      riskManagerId: data.riskManagerId ?? [],
    });
  } else {
    message.warning('正在变更中，不能移交');
  }
};
const activeKey = ref('info');
const changeTab = async (key: string) => {
  await (key === 'info' ? gridApi.setGridOptions(gridOptions) : gridApi.setGridOptions(changeGridOptions));
  await gridApi.reload();
  // gridApi.formApi.submitForm();
};

onMounted(() => {
  loadData();
});
</script>

<template>
  <Page auto-content-height>
    <Grid v-if="dataLoaded">
      <template #form-append>
        <a-tabs v-model:active-key="activeKey" @change="changeTab">
          <a-tab-pane key="info" tab="项目信息" />
          <a-tab-pane key="change" tab="变更记录" />
        </a-tabs>
      </template>
      <template #toolbar-actions>
        <a-space v-if="activeKey === 'info'">
          <Button type="primary" @click="transfer()"> {{ $t('base.transfer') }}</Button>
          <Button type="primary" @click="change()"> {{ $t('base.change') }}</Button>
          <Button type="primary" @click="complete()"> {{ $t('base.completed') }}</Button>
        </a-space>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="detail(row)">
            {{ $t('base.detail') }}
          </TypographyLink>
          <!--<TypographyLink-->
          <!--  v-if="row.changeStatus !== 'UNDER_REVIEW' && row.status !== 'COMPLETED'"-->
          <!--  @click="transfer(row)"-->
          <!--&gt;-->
          <!--  {{ $t('base.transfer') }}-->
          <!--</TypographyLink>-->
          <!--<TypographyLink v-if="row.changeStatus !== 'UNDER_REVIEW' && row.status !== 'COMPLETED'" @click="change(row)">-->
          <!--  {{ $t('base.change') }}-->
          <!--</TypographyLink>-->
          <TypographyLink v-if="row.status === 'COMPLETED' && activeKey === 'info'" @click="cancelComplete(row)">
            {{ $t('base.cancel') }}
          </TypographyLink>
          <!--<TypographyLink-->
          <!--  v-if="-->
          <!--    row.status === 'EFFECTIVE' &&-->
          <!--    (row.changeStatus === 'NOT_CHANGE' ||-->
          <!--      row.changeStatus === 'REVIEWED' ||-->
          <!--      row.changeStatus === 'NOT_REVIEWED')-->
          <!--  "-->
          <!--  @click="complete(row)"-->
          <!--&gt;-->
          <!--  {{ $t('base.completed') }}-->
          <!--</TypographyLink>-->
        </Space>
      </template>
    </Grid>
    <div v-else class="flex h-64 items-center justify-center">
      <Spin size="large" />
    </div>
    <Detail @register="registerDetailForm" @ok="detailSuccess" />
    <Change @register="registerChangeForm" @ok="changeSuccess" />

    <Modal
      v-model:open="manageTransferModal.visible"
      title="移交项目"
      @ok="handleTransferOk"
      @cancel="handleTransferCancel"
      width="800px"
    >
      <Form
        :colon="false"
        :model="transferForm"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="{ span: 20 }"
        class="px-8"
      >
        <FormItem label="业务负责人" name="businessManagerId">
          <FeUserSelect
            v-model:value="transferForm.businessManagerId"
            multiple
            :api="{ getUserInfoByIdsApi, getUserListByKeywordApi, getUserTreeListApi, getUserListApi }"
          />
        </FormItem>
        <FormItem label="运营负责人" name="operationManagerId">
          <FeUserSelect
            v-model:value="transferForm.operationManagerId"
            multiple
            :api="{ getUserInfoByIdsApi, getUserListByKeywordApi, getUserTreeListApi, getUserListApi }"
          />
        </FormItem>
        <FormItem label="财务负责人" name="financeManagerId">
          <FeUserSelect
            v-model:value="transferForm.financeManagerId"
            multiple
            :api="{ getUserInfoByIdsApi, getUserListByKeywordApi, getUserTreeListApi, getUserListApi }"
          />
        </FormItem>
        <FormItem label="风控负责人" name="riskManagerId">
          <FeUserSelect
            v-model:value="transferForm.riskManagerId"
            multiple
            :api="{ getUserInfoByIdsApi, getUserListByKeywordApi, getUserTreeListApi, getUserListApi }"
          />
        </FormItem>
      </Form>
    </Modal>
  </Page>
</template>

<style></style>
