<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';
import type { SelectValue } from 'ant-design-vue/es/select';
import type { VxeGridInstance } from 'vxe-table';

import type { WorkflowStartInfo } from '@vben/types';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type { ProjectBaseInfo, ProjectPartners } from '#/api';

import { computed, nextTick, reactive, ref, watch } from 'vue';

import { BasicPopup, FeUserSelect, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { cloneDeep, getCombinedErrorMessagesString } from '@vben/utils';

import {
  Button,
  Col,
  DatePicker,
  Form,
  FormItem,
  Input,
  InputNumber,
  message,
  Modal,
  Row,
  Select,
  Table,
  Textarea,
  Typography,
} from 'ant-design-vue';

import { BaseAttachmentList, BaseRegionPicker } from '#/adapter/base-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  BusinessStructureEnum,
  getCompanyApi,
  getUserInfoByIdsApi,
  getUserListApi,
  getUserListByKeywordApi,
  getUserTreeListApi,
  projectProposalAddApi,
  projectProposalDetailApi,
  projectProposalEditApi,
  projectProposalSubmitApi,
} from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';

type GridApi = { grid: VxeGridInstance };

const emit = defineEmits(['register', 'ok']);

const { startWorkflow, WorkflowPreviewModal, useOperationButton, initWorkflow, isProcessInstance, isWorkflowLoading } =
  useWorkflowBase();
const pageType = ref('edit');
const OperationButton = useOperationButton();
const { getDictList } = useDictStore();
const colSpan = { md: 12, sm: 24 };
const pageLoading = ref(false);
const loading = reactive({
  submit: false,
});
const nonEditStatus = 'DRAFTING';

// 根据接口定义初始化产品信息
const defaultForm = {
  id: undefined,
  createTime: undefined,
  createBy: undefined,
  updateTime: undefined,
  updateBy: undefined,
  version: undefined,
  projectCode: undefined,
  projectName: undefined,
  executorCompanyCode: undefined,
  executorCompanyName: '江西财投集团有限责任公司',
  businessStructure: undefined,
  projectModel: undefined,
  purchaseMode: [],
  isGoodsControlMode: 0,
  paymentTermDays: undefined,
  planStartDate: undefined,
  creditDueDate: undefined,
  creditAmount: undefined,
  serviceFeeRate: undefined,
  serviceFeeType: undefined,
  serviceFeeRateJson: undefined,
  province: undefined,
  city: undefined,
  district: undefined,
  detailAddress: undefined,
  remarks: undefined,
  isDeposit: 0,
  mortgageInfoDesc: undefined,
  pledgeInfoDesc: undefined,
  mortgageInfoAttachment: undefined,
  pledgeInfoAttachment: undefined,
  guaranteeInfoDesc: undefined,
  riskControlDesc: undefined,
  creditEnhancementDesc: undefined,
  status: undefined,
  approvalStatus: undefined,
  paymentMethod: [],
  collectionMethod: [],
  settlementMethod: undefined,
  isKeyIndustry: 0,
  isRealEnterprise: 0,
  businessManagerId: [],
  operationManagerId: [],
  financeManagerId: [],
  riskManagerId: [],
  guaranteeCompanyCode: undefined,
  guaranteeCompanyName: undefined,
  creditType: undefined,
  projectReviewId: undefined,
  partyBranchId: undefined,
  generalManagerId: undefined,
  reviewNodeId: undefined,
  attachmentList: [],
  projectPartners: [
    {
      id: undefined,
      version: undefined,
      projectId: undefined,
      partnerType: undefined,
      companyCode: undefined,
      companyName: undefined,
      subLimitAmount: undefined,
      occupyLimit: undefined,
      creditType: undefined,
      expiryDate: undefined,
    },
  ],
};

const detailForm = reactive<Partial<ProjectBaseInfo & WorkflowStartInfo>>(cloneDeep(defaultForm));

// 根据接口必填字段定义验证规则
const rules: Record<string, Rule[]> = {
  projectName: [{ required: true, message: '请输入项目名称', trigger: 'change' }],
  businessStructure: [{ required: true, message: '请选择业务结构', trigger: 'change' }],
  projectModel: [{ required: true, message: '请选择项目模式', trigger: 'change' }],
  executorCompanyName: [{ required: true, message: '请输入贸易执行企业', trigger: 'change' }],
  businessManagerId: [{ required: true, message: '请选择业务负责人', trigger: 'change', type: 'array' }],
  operationManagerId: [{ required: true, message: '请选择运营负责人', trigger: 'change', type: 'array' }],
  financeManagerId: [{ required: true, message: '请选择财务负责人', trigger: 'change', type: 'array' }],
  riskManagerId: [{ required: true, message: '请选择风控负责人', trigger: 'change', type: 'array' }],
  settlementMethod: [{ required: true, message: '请选择结算方式', trigger: 'change' }],
  isDeposit: [{ required: true, message: '请选择是否有保证金', trigger: 'change' }],
  isGoodsControlMode: [{ required: true, message: '请选择是否控货模式', trigger: 'change' }],
  serviceFeeRate: [{ required: true, message: '请输入合作费率', trigger: 'change' }],
  paymentTermDays: [{ required: true, message: '请输入账期', trigger: 'change' }],
  creditAmount: [{ required: true, message: '请输入授信额度', trigger: 'change' }],
  isKeyIndustry: [{ required: true, message: '请选择是否支持重点产业链', trigger: 'change' }],
  isRealEnterprise: [{ required: true, message: '请选择是否支持实体企业', trigger: 'change' }],
  creditDueDate: [{ required: true, message: '请选择授信到期日期', trigger: 'change' }],
  creditType: [{ required: true, message: '请选择授信类型', trigger: 'change' }],
  isDirectShipment: [{ required: true, message: '请选择是否直接发运', trigger: 'change' }],
  isPriceFixed: [{ required: true, message: '请选择是否支持点价', trigger: 'change' }],
};

const companyOptions = ref([]);
// 获取企业列表
const getCompanyList = async () => {
  const res = await getCompanyApi();
  Object.assign(companyOptions.value, res);
};

// 处理逗号分隔字符串的函数
const processCommaSeparatedField = (fieldValue: any): string | undefined => {
  if (Array.isArray(fieldValue)) {
    return fieldValue.join(',');
  } else if (typeof fieldValue === 'string') {
    return fieldValue;
  }
  return undefined;
};

// 解析逗号分隔字符串为数组的函数
const parseCommaSeparatedField = (fieldValue: any): string[] => {
  if (typeof fieldValue === 'string' && fieldValue) {
    return fieldValue.split(',').filter((item) => item !== '');
  } else if (Array.isArray(fieldValue)) {
    return fieldValue;
  }
  return [];
};

const formRef = ref();
const gridCreditRef = ref();

const workflowSuccess = () => {
  emit('ok');
  closePopup();
};

const init = async (data: ProjectBaseInfo & { pageType: string }) => {
  pageLoading.value = true;
  pageType.value = data.pageType;
  await getCompanyList();
  await initWorkflow({ formKey: 'scm_project', businessKey: data.id });
  if (data.id) {
    const res: Partial<ProjectBaseInfo> = await projectProposalDetailApi(data.id);
    Object.assign(detailForm, res);

    detailForm.purchaseMode = parseCommaSeparatedField(detailForm.purchaseMode);
    detailForm.paymentMethod = parseCommaSeparatedField(detailForm.paymentMethod);
    detailForm.collectionMethod = parseCommaSeparatedField(detailForm.collectionMethod);

    // 处理合作费率表格数据
    if (detailForm.serviceFeeRateJson) {
      try {
        const feeRateData = JSON.parse(detailForm.serviceFeeRateJson);
        if (feeRateData.headerInputs) {
          serviceFeeRateHeaderInputs.value = feeRateData.headerInputs;
        }
        if (feeRateData.rateList) {
          serviceFeeRateList.value = feeRateData.rateList;
        }
      } catch (error) {
        console.error('解析合作费率数据失败:', error);
      }
    }

    // 强制校验并转换projectPartners字段
    detailForm.projectPartners =
      !Array.isArray(res.projectPartners) || res.projectPartners === null ? [] : [...res.projectPartners];

    // 强制刷新表格
    if (gridApiSupplier?.grid) {
      await gridApiSupplier.grid.reloadData(
        detailForm.projectPartners.filter((item) => item.partnerType === 'SUPPLIER'),
      );
    }
    if (gridApiPurchaser?.grid) {
      await gridApiPurchaser.grid.reloadData(
        detailForm.projectPartners.filter((item) => item.partnerType === 'PURCHASER'),
      );
    }
    if (gridApiCredit?.grid) {
      await gridApiCredit.grid.reloadData(
        detailForm.projectPartners.filter((item) => item.partnerType === 'CREDIT_COMPANY'),
      );
    }
  }
  pageLoading.value = false;
};

const title = computed(() => {
  return detailForm.id ? '编辑项目' : '新增项目';
});

const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const close = () => {
  Object.assign(detailForm, cloneDeep(defaultForm));
  // 重置合作费率相关数据
  serviceFeeRateList.value = [];
  serviceFeeRateHeaderInputs.value = {
    minAmount: 15000,
    maxAmount: 15000,
  };
};

const accountList = ref<ProjectPartners[]>([]);

const save = async (type: string) => {
  await formRef.value.validate();

  const errMap = await gridApiCredit.grid?.validate(true);
  if (errMap) {
    const errMessage = getCombinedErrorMessagesString(errMap);
    if (errMessage) {
      message.error(errMessage);
    }
    return;
  }
  changeOkLoading(true);

  let api = detailForm.id ? projectProposalEditApi : projectProposalAddApi;
  if (type === 'submit') {
    const { processDefinitionKey, startUserSelectAssignees } = await startWorkflow();
    detailForm.processDefinitionKey = processDefinitionKey;
    detailForm.startUserSelectAssignees = startUserSelectAssignees;
    api = projectProposalSubmitApi;
  }

  const submitData = { ...detailForm };
  submitData.purchaseMode = processCommaSeparatedField(detailForm.purchaseMode);
  submitData.paymentMethod = processCommaSeparatedField(detailForm.paymentMethod);
  submitData.collectionMethod = processCommaSeparatedField(detailForm.collectionMethod);

  // 处理合作费率表格数据
  if (detailForm.serviceFeeType === '2' && serviceFeeRateList.value.length > 0) {
    submitData.serviceFeeRateJson = JSON.stringify({
      headerInputs: serviceFeeRateHeaderInputs.value,
      rateList: serviceFeeRateList.value
    });
  }

  try {
    const supplierData =
      gridApiSupplier.grid?.getTableData().tableData.map((row) => ({
        ...row,
        partnerType: 'SUPPLIER', // 标记为上游企业
      })) || [];
    const purchaserData =
      gridApiPurchaser.grid?.getTableData().tableData.map((row) => ({
        ...row,
        partnerType: 'PURCHASER', // 标记为下游企业
      })) || [];
    const creditData =
      gridApiCredit.grid?.getTableData().tableData.map((row) => ({
        ...row,
        partnerType: 'CREDIT_COMPANY', // 标记为终端企业
      })) || [];

    submitData.projectPartners = [...supplierData, ...purchaserData, ...creditData];

    // 调用接口提交
    const res = await api(submitData as ProjectBaseInfo);

    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } finally {
    changeOkLoading(false);
  }
  close();
};

// 新增初始化方法
const setAccountData = (data: ProjectPartners[]) => {
  accountList.value = data;
  // 更新所有相关表格
  if (gridApiSupplier.grid) {
    gridApiSupplier.grid.reloadData(accountList.value);
  }
  if (gridApiPurchaser.grid) {
    gridApiPurchaser.grid.reloadData(accountList.value);
  }
  if (gridApiCredit.grid) {
    gridApiCredit.grid.reloadData(accountList.value);
  }
};

const gridOptions: VxeGridProps = {
  showOverflow: 'title',
  keepSource: true,
  border: 'inner',
  editConfig: {
    trigger: 'click',
    mode: 'row',
    autoClear: false,
  },
  rowConfig: {
    drag: true,
  },
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbarTools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};

const gridOptionsSupplier: VxeGridProps = {
  ...gridOptions,
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    {
      field: 'companyName',
      title: '企业名称',
      slots: { default: 'companyName' },
      minWidth: '160px',
    },
    {
      field: 'companyCode',
      title: '统一社会信用代码',
      slots: { default: 'companyCode' },
      minWidth: '160px',
    },
  ],
  data: [],
};

const gridOptionsPurchaser: VxeGridProps = {
  ...gridOptions,
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    {
      field: 'companyName',
      title: '企业名称',
      slots: { default: 'companyName' },
      minWidth: '160px',
    },
    {
      field: 'companyCode',
      title: '统一社会信用代码',
      slots: { default: 'companyCode' },
      minWidth: '160px',
    },
  ],
  data: [],
};

const gridOptionsCredit: VxeGridProps = {
  ...gridOptions,
  editRules: {
    subLimitAmount: [
      { required: true, message: '请输入企业额度上限' },
      {
        validator: ({ cellValue }) => {
          if (detailForm.creditAmount && cellValue > detailForm.creditAmount) {
            // UX: 润色校验文案
            return new Error('企业额度上限不可超过授信额度');
          }
        },
      },
    ],
    occupyLimit: [{ required: true, message: '请选择是否占用企业总额度' }],
  },
  columns: [
    {
      type: 'checkbox',
      width: '60px',
      fixed: 'left',
    },
    {
      field: 'companyName',
      title: '企业名称',
      slots: { default: 'companyName' },
      minWidth: '160px',
    },
    {
      field: 'companyCode',
      title: '统一社会信用代码',
      slots: { default: 'companyCode' },
      minWidth: '160px',
    },
    {
      field: 'subLimitAmount',
      title: '企业额度上限（元）',
      slots: { default: 'edit_sub_limit_amount' },
      minWidth: '160px',
    },
    {
      field: 'occupyLimit',
      title: '是否占用企业总额度',
      slots: { default: 'occupyLimit' },
      formatter: ['formatStatus', 'baseBooleanType'],
      minWidth: '160px',
    },
  ],
  data: [],
};

interface CompanySelectorModal {
  visible: boolean;
  gridApi: GridApi | null;
  company: {
    companyCode: string | undefined;
    companyName: string | undefined;
    createBy: number;
    createTime: string | undefined;
    projectId: number;
    updateBy: number;
    updateTime: string | undefined;
    version: number;
  };
}

const companySelectorModal = reactive<CompanySelectorModal>({
  visible: false,
  gridApi: null as GridApi | null,
  company: {
    companyName: undefined,
    companyCode: undefined,
    createBy: 0,
    createTime: undefined,
    updateBy: 0,
    updateTime: undefined,
    version: 0,
    projectId: detailForm.id || 0,
  },
});

// 处理企业选择变化
const handleCompanyChange = (value: SelectValue, option: any) => {
  if (value && option) {
    companySelectorModal.company.companyCode = value as string;
    companySelectorModal.company.companyName = option.companyName;
  } else {
    companySelectorModal.company.companyCode = undefined;
    companySelectorModal.company.companyName = undefined;
  }
};

// 检查企业是否重复的函数
const checkDuplicateCompany = (companyCode: string, gridApi: GridApi): boolean => {
  if (!companyCode || !gridApi?.grid) return false;

  const existingData = gridApi.grid.getTableData().tableData;
  return existingData.some((item: any) => item.companyCode === companyCode);
};

// 检查企业在所有类型中是否重复的函数
const checkDuplicateCompanyInAllTypes = (companyCode: string, currentGridApi: GridApi): boolean => {
  if (!companyCode) return false;

  // 检查上游企业
  if (currentGridApi !== gridApiSupplier && gridApiSupplier?.grid) {
    const supplierData = gridApiSupplier.grid.getTableData().tableData;
    if (supplierData.some((item: any) => item.companyCode === companyCode)) {
      return true;
    }
  }

  // 检查下游企业
  if (currentGridApi !== gridApiPurchaser && gridApiPurchaser?.grid) {
    const purchaserData = gridApiPurchaser.grid.getTableData().tableData;

    if (purchaserData.some((item: any) => item.companyCode === companyCode)) {
      return true;
    }
  }

  // 检查授信企业
  if (currentGridApi !== gridApiCredit && gridApiCredit?.grid) {
    const creditData = gridApiCredit.grid.getTableData().tableData;
    if (creditData.some((item: any) => item.companyCode === companyCode)) {
      return true;
    }
  }

  // 检查当前表格中是否重复
  return checkDuplicateCompany(companyCode, currentGridApi);
};

const handleCompanySelectOk = () => {
  if (companySelectorModal.company && companySelectorModal.gridApi) {
    try {
      const data = companySelectorModal.company;
      const companyCode = data.companyCode ?? '';

      // 检查企业是否重复
      if (checkDuplicateCompanyInAllTypes(companyCode, companySelectorModal.gridApi)) {
        message.warning('该企业已存在，不能重复添加');
        return;
      }

      companySelectorModal.gridApi.grid.insertAt(data, -1);
      syncCreditCompanyTable();
    } catch (error) {
      message.error(`添加企业失败: ${(error as Error).message}`);
    }
  }
  companySelectorModal.visible = false;
  companySelectorModal.company.companyCode = undefined;
};

const handleCompanySelectCancel = () => {
  companySelectorModal.visible = false;
  companySelectorModal.company.companyCode = undefined;
};

// 选择企业
const selectCompany = async (gridApi: GridApi) => {
  if (isAddSupplierDisabled.value && gridApi === gridApiSupplier) {
    message.warning('此模式下仅支持添加一家上游企业');
    return;
  }
  if (isAddPurchaserDisabled.value && gridApi === gridApiPurchaser) {
    message.warning('此模式下仅支持添加一家下游企业');
    return;
  }

  if (gridApi) {
    companySelectorModal.visible = true;
    companySelectorModal.gridApi = gridApi;
  }
};

// 删除行
const removeAccount = async (gridApi: GridApi) => {
  const $grid = gridApi.grid;
  if ($grid) {
    const selectRecords = $grid.getCheckboxRecords();
    if (!selectRecords || selectRecords.length === 0) {
      message.warning('请选择要删除的数据');
      return;
    }

    const { deleteProjectPartners } = detailForm;

    detailForm.deleteProjectPartners = Array.isArray(deleteProjectPartners)
      ? [...deleteProjectPartners, ...selectRecords]
      : [...selectRecords];

    $grid.remove(selectRecords);
    syncCreditCompanyTable();
  }
};

const [GridSupplier, gridApiSupplier] = useVbenVxeGrid({
  gridOptions: gridOptionsSupplier,
});
const [GridPurchaser, gridApiPurchaser] = useVbenVxeGrid({
  gridOptions: gridOptionsPurchaser,
});
const [GridCredit, gridApiCredit] = useVbenVxeGrid({
  gridOptions: gridOptionsCredit,
});

const filteredProjectModeOptions = ref<any[]>([]);

const isAddSupplierDisabled = computed(() => {
  const isSaleAndIndustry = detailForm.businessStructure === 'SALE' && detailForm.projectModel === 'INDUSTRY';
  if (!isSaleAndIndustry) return false;
  const supplierData = gridApiSupplier.grid?.getTableData().tableData || [];
  return supplierData.length > 0;
});

const isAddPurchaserDisabled = computed(() => {
  const isPurchaseAndIndustry = detailForm.businessStructure === 'PURCHASE' && detailForm.projectModel === 'INDUSTRY';
  if (!isPurchaseAndIndustry) return false;
  const purchaserData = gridApiPurchaser.grid?.getTableData().tableData || [];
  return purchaserData.length > 0;
});

// const isCreditGridLocked = computed(() => {
//   return detailForm.businessStructure === 'SALE' || detailForm.businessStructure === 'PURCHASE';
// });

const syncCreditCompanyTable = () => {
  // FIX: 添加核心安全守卫，防止在 grid 未初始化时调用
  if (!gridApiCredit.grid || !gridApiSupplier.grid || !gridApiPurchaser.grid) {
    return;
  }

  let sourceData: ProjectPartners[] = [];

  if (detailForm.businessStructure === 'SALE') {
    sourceData = gridApiSupplier.grid.getTableData().tableData;
  } else if (detailForm.businessStructure === 'PURCHASE') {
    sourceData = gridApiPurchaser.grid.getTableData().tableData;
  } else {
    gridApiCredit.grid.reloadData([]);
    return;
  }

  const newCreditData = sourceData.map((item) => ({
    companyCode: item.companyCode,
    companyName: item.companyName,
    subLimitAmount: undefined,
    occupyLimit: 0,
    creditType: undefined,
  }));

  gridApiCredit.grid.reloadData(newCreditData);
};

const handleBusinessStructureChange = () => {
  detailForm.projectModel = undefined;
};

watch(
  [() => detailForm.businessStructure, () => detailForm.projectModel],
  async ([businessStructure, projectModel]) => {
    await nextTick();
    const options = getDictList('PROJECT_MODE');
    filteredProjectModeOptions.value =
      detailForm.id && projectModel
        ? options
        : options.map((option) => {
            if (businessStructure === 'SALE' && (option.value === 'BUILDING' || option.value === 'WAREHOUSE_SUPPLY')) {
              return { ...option, disabled: true };
            }
            return { ...option, disabled: false };
          });

    const supplierData = gridApiSupplier?.grid?.getTableData().tableData || [];
    if (businessStructure === 'SALE' && projectModel === 'INDUSTRY' && supplierData.length > 1) {
      message.warning('为符合“产业模式”单企业要求，上游企业列表已自动清空');
      gridApiSupplier.grid?.reloadData([]);
    }

    const purchaserData = gridApiPurchaser.grid?.getTableData().tableData || [];
    if (businessStructure === 'PURCHASE' && projectModel === 'INDUSTRY' && purchaserData.length > 1) {
      message.warning('为符合“产业模式”单企业要求，下游企业列表已自动清空');
      gridApiPurchaser.grid?.reloadData([]);
    }

    syncCreditCompanyTable();
  },
);
// 合作费率表格相关
const serviceFeeRateHeaderInputs = ref({
  minAmount: 15000,
  maxAmount: 15000,
});

const serviceFeeRateColumns = ref([
  {
    title: '账期（天）/合作规模（万元）',
    dataIndex: 'paymentTermDays',
    width: 250,
    key: 'paymentTermDays'
  },
  {
    title: 'X≤',
    dataIndex: 'minAmount',
    key: 'minAmount',
    width: 150
  },
  {
    title: 'X>',
    dataIndex: 'maxAmount',
    key: 'maxAmount',
    width: 150
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 80
  },
]);

const serviceFeeRateList = ref([]);

const addServiceFeeRate = () => {
  const newIndex = serviceFeeRateList.value.length;
  const lastItem = serviceFeeRateList.value[newIndex - 1];

  serviceFeeRateList.value.push({
    startRange: newIndex === 0 ? 1 : (lastItem?.endRange ? lastItem.endRange + 1 : 1),
    endRange: undefined,
    minAmount: '',
    maxAmount: '',
  });
};

const delServiceFeeRate = (index: number) => {
  serviceFeeRateList.value.splice(index, 1);
  // 重新计算所有行的开始范围
  updateRanges();
};

// 更新范围联动
const updateRanges = () => {
  serviceFeeRateList.value.forEach((item, index) => {
    if (index === 0) {
      item.startRange = 1;
    } else {
      const prevItem = serviceFeeRateList.value[index - 1];
      item.startRange = prevItem.endRange ? prevItem.endRange + 1 : 1;
    }
  });
};

// 处理结束范围变化
const handleEndRangeChange = (index: number, value: number) => {
  serviceFeeRateList.value[index].endRange = value;
  // 更新后续行的开始范围
  for (let i = index + 1; i < serviceFeeRateList.value.length; i++) {
    const prevItem = serviceFeeRateList.value[i - 1];
    serviceFeeRateList.value[i].startRange = prevItem.endRange ? prevItem.endRange + 1 : 1;
  }
};

defineExpose({
  getAccountData() {
    let allData: any[] = [];

    if (gridApiSupplier.grid) {
      allData = [...allData, ...gridApiSupplier.grid.getTableData().tableData];
    }

    if (gridApiPurchaser.grid) {
      allData = [...allData, ...gridApiPurchaser.grid.getTableData().tableData];
    }

    if (gridApiCredit.grid) {
      allData = [...allData, ...gridApiCredit.grid.getTableData().tableData];
    }

    return allData;
  },
  setAccountData,
});
</script>

<template>
  <BasicPopup v-bind="$attrs" :loading="pageLoading" :title="title" @register="registerPopup" @close="close">
    <template #insertToolbar>
      <a-space>
        <a-button
          v-if="!detailForm.id || detailForm.status === nonEditStatus || pageType === 'edit'"
          type="primary"
          :loading="loading.submit"
          @click="save('save')"
        >
          保存
        </a-button>
        <a-button
          v-if="!detailForm.id || detailForm.status === nonEditStatus || pageType === 'edit'"
          type="primary"
          :loading="loading.submit"
          @click="save('submit')"
        >
          提交
        </a-button>
      </a-space>
      <div v-if="pageType === 'audit'" v-loading="isWorkflowLoading">
        <OperationButton v-if="isProcessInstance" @success="workflowSuccess" />
      </div>
    </template>
    <Form
      ref="formRef"
      :colon="false"
      :model="detailForm"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
      class="px-8"
    >
      <BasicCaption v-if="!detailForm.id" content="基本信息" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="项目编号" name="projectCode">
            <Input
              v-model:value="detailForm.projectCode"
              placeholder="留空自动生成"
              :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="项目名称" name="projectName">
            <Input
              v-model:value="detailForm.projectName"
              :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="业务结构" name="businessStructure">
            <Select
              v-model:value="detailForm.businessStructure"
              :options="getDictList('BUS_STRUCTURE')"
              formatter="['formatstatus','BUS_STRUCTURE']"
              :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
              @change="handleBusinessStructureChange"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="项目模式" name="projectModel">
            <Select
              v-model:value="detailForm.projectModel"
              :options="detailForm.id ? getDictList('PROJECT_MODE') : filteredProjectModeOptions"
              formatter="['formatstatus','PROJECT_MODE']"
              :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="贸易执行企业" name="executorCompanyName">
            <Input v-model:value="detailForm.executorCompanyName" readonly />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="担保企业" name="guaranteeCompanyCode">
            <Select
              v-model:value="detailForm.guaranteeCompanyCode"
              :options="companyOptions"
              :field-names="{ label: 'companyName', value: 'companyCode' }"
              show-search
              :filter-option="
                (input: string, option: any) => option.companyName.toLowerCase().includes(input.toLowerCase())
              "
              :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="是否有保证金" name="isDeposit">
            <Select
              v-model:value="detailForm.isDeposit"
              :options="getDictList('baseBooleanType')"
              formatter="['formatstatus','baseBooleanType']"
              :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="是否直接发运" name="isDirectShipment">
            <Select
              v-model:value="detailForm.isDirectShipment"
              :options="getDictList('baseBooleanType')"
              formatter="['formatstatus','baseBooleanType']"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="是否控货模式" name="isGoodsControlMode">
            <Select
              v-model:value="detailForm.isGoodsControlMode"
              :options="getDictList('baseBooleanType')"
              formatter="['formatstatus','baseBooleanType']"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="是否支持点价" name="isPriceFixed">
            <Select
              v-model:value="detailForm.isPriceFixed"
              :options="[
                { label: '是', value: 0 },
                { label: '否', value: 1 },
              ]"
            />
          </FormItem>
        </Col>
        <Col v-if="detailForm.businessStructure !== 'GENERAL'" v-bind="colSpan">
          <FormItem label="授信额度(元)" name="creditAmount">
            <InputNumber
              v-model:value="detailForm.creditAmount"
              :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
            />
          </FormItem>
        </Col>
        <Col v-if="detailForm.businessStructure !== 'GENERAL'" v-bind="colSpan">
          <FormItem label="账期(天)" name="paymentTermDays">
            <InputNumber
              v-model:value="detailForm.paymentTermDays"
              :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
            />
          </FormItem>
        </Col>
        <Col v-if="detailForm.businessStructure !== 'GENERAL'" v-bind="colSpan">
          <FormItem label="结算方式" name="settlementMethod">
            <Select
              v-model:value="detailForm.settlementMethod"
              :options="getDictList('SETTLEMENT_MODE')"
              formatter="['formatstatus', 'SETTLEMENT_MODE']"
              :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
            />
          </FormItem>
        </Col>
        <Col v-if="detailForm.businessStructure !== 'GENERAL'" v-bind="colSpan">
          <FormItem label="授信类型" name="creditType">
            <Select
              v-model:value="detailForm.creditType"
              :options="getDictList('CREDIT_TYPE')"
              formatter="['formatstatus', 'CREDIT_TYPE']"
              :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
            />
          </FormItem>
        </Col>
        <Col v-if="detailForm.businessStructure !== 'GENERAL'" v-bind="colSpan">
          <FormItem label="授信到期日期" name="creditDueDate">
            <DatePicker
              v-model:value="detailForm.creditDueDate"
              value-format="YYYY-MM-DD hh:mm:ss"
              format="YYYY-MM-DD"
              :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="采购模式" name="purchaseMode">
            <Select
              v-model:value="detailForm.purchaseMode"
              mode="multiple"
              :options="getDictList('PURCHASE_MODE')"
              formatter="['formatstatus','PURCHASE_MODE']"
              :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="预计开始日期" name="planStartDate">
            <DatePicker
              v-model:value="detailForm.planStartDate"
              value-format="YYYY-MM-DD hh:mm:ss"
              format="YYYY-MM-DD"
              :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="付款方式" name="paymentMethod">
            <Select
              v-model:value="detailForm.paymentMethod"
              mode="multiple"
              :options="getDictList('PAYMENT_WAY')"
              formatter="['formatstatus','PAYMENT_WAY']"
              :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="回款方式" name="collectionMethod">
            <Select
              v-model:value="detailForm.collectionMethod"
              mode="multiple"
              :options="getDictList('PAYMENT_WAY')"
              formatter="['formatstatus','PAYMENT_WAY']"
              :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="是否支持重点产业链" name="isKeyIndustry">
            <Select
              v-model:value="detailForm.isKeyIndustry"
              :options="getDictList('baseBooleanType')"
              :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="是否支持实体企业" name="isRealEnterprise">
            <Select
              v-model:value="detailForm.isRealEnterprise"
              :options="getDictList('baseBooleanType')"
              :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="业务负责人" name="businessManagerId">
            <FeUserSelect
              v-model:value="detailForm.businessManagerId"
              multiple
              :api="{ getUserInfoByIdsApi, getUserListByKeywordApi, getUserTreeListApi, getUserListApi }"
              :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="运营负责人" name="operationManagerId">
            <FeUserSelect
              v-model:value="detailForm.operationManagerId"
              multiple
              :api="{ getUserInfoByIdsApi, getUserListByKeywordApi, getUserTreeListApi, getUserListApi }"
              :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="财务负责人" name="financeManagerId">
            <FeUserSelect
              v-model:value="detailForm.financeManagerId"
              multiple
              :api="{ getUserInfoByIdsApi, getUserListByKeywordApi, getUserTreeListApi, getUserListApi }"
              :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="风控负责人" name="riskManagerId">
            <FeUserSelect
              v-model:value="detailForm.riskManagerId"
              multiple
              :api="{ getUserInfoByIdsApi, getUserListByKeywordApi, getUserTreeListApi, getUserListApi }"
              :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <Row :gutter="12">
            <Col :span="12">
              <FormItem
                label="项目地点"
                name="detailAddress"
                v-bind="{ labelCol: { span: 12 }, wrapperCol: { span: 12 } }"
              >
                <BaseRegionPicker
                  v-model:province="detailForm.province"
                  v-model:city="detailForm.city"
                  v-model:district="detailForm.district"
                  :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
                />
              </FormItem>
            </Col>
            <Col :span="12">
              <FormItem name="detailAddress" :wrapper-col="{ span: 24 }">
                <Input
                  v-model:value="detailForm.detailAddress"
                  :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
                  class="w-full"
                />
              </FormItem>
            </Col>
          </Row>
        </Col>
        <template v-if="detailForm.businessStructure !== BusinessStructureEnum.GENERAL">
          <Col v-bind="colSpan">
            <FormItem label="合作费率类型" name="serviceFeeType">
              <Select v-model:value="detailForm.serviceFeeType" :options="getDictList('serviceFeeType')" />
            </FormItem>
          </Col>
          <Col v-if="detailForm.serviceFeeType === '1'" v-bind="colSpan">
            <FormItem label="合作费率(年%)" name="serviceFeeRate">
              <InputNumber
                v-model:value="detailForm.serviceFeeRate"
                :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
              />
            </FormItem>
          </Col>
          <Col v-if="detailForm.serviceFeeType === '2'" :span="24">
            <FormItem
              label="合作费率(年%)"
              name="serviceFeeRateJson"
              v-bind="{ labelCol: { span: 3 }, wrapperCol: { span: 21 } }"
            >
              <Button type="primary" @click="addServiceFeeRate" class="mb-2">新增区间</Button>

              <Table
                :data-source="serviceFeeRateList"
                :columns="serviceFeeRateColumns"
                :pagination="false"
                bordered
                class="service-fee-rate-table"
              >
                <template #headerCell="{ column }">
                  <template v-if="column.key === 'minAmount'">
                    <div class="flex items-center gap-1">
                      <span>X≤</span>
                      <InputNumber
                        v-model:value="serviceFeeRateHeaderInputs.minAmount"
                        :min="0"
                        placeholder="15000"
                        style="width: 80px"
                        size="small"
                        :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
                      />
                    </div>
                  </template>
                  <template v-else-if="column.key === 'maxAmount'">
                    <div class="flex items-center gap-1">
                      <span>X></span>
                      <InputNumber
                        v-model:value="serviceFeeRateHeaderInputs.maxAmount"
                        :min="0"
                        placeholder="15000"
                        style="width: 80px"
                        size="small"
                        :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
                      />
                    </div>
                  </template>
                  <template v-else>
                    {{ column.title }}
                  </template>
                </template>

                <template #bodyCell="{ column, record, index }">
                  <template v-if="column.key === 'paymentTermDays'">
                    <div class="flex items-center gap-1">
                      <InputNumber
                        :value="record.startRange"
                        :disabled="true"
                        style="width: 60px"
                        size="small"
                      />
                      <span>~</span>
                      <template v-if="index === serviceFeeRateList.length - 1">
                        <div class="flex items-center justify-center" style="width: 60px; height: 24px; border: 1px solid #d9d9d9; border-radius: 2px; background-color: #f5f5f5;">
                          ∞
                        </div>
                      </template>
                      <template v-else>
                        <InputNumber
                          v-model:value="record.endRange"
                          :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
                          placeholder="59"
                          style="width: 60px"
                          size="small"
                          @change="(value) => handleEndRangeChange(index, value)"
                        />
                      </template>
                    </div>
                  </template>
                  <template v-else-if="column.key === 'minAmount'">
                    <InputNumber
                      v-model:value="record.minAmount"
                      placeholder="6.5"
                      :min="0"
                      :max="100"
                      :precision="1"
                      :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
                      style="width: 100%"
                    />
                  </template>
                  <template v-else-if="column.key === 'maxAmount'">
                    <InputNumber
                      v-model:value="record.maxAmount"
                      placeholder="5.5"
                      :min="0"
                      :max="100"
                      :precision="1"
                      :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
                      style="width: 100%"
                    />
                  </template>
                  <template v-else-if="column.key === 'action'">
                    <Typography.Link
                      type="danger"
                      @click="delServiceFeeRate(index)"
                      :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
                    >
                      删除
                    </Typography.Link>
                  </template>
                </template>
              </Table>
            </FormItem>
          </Col>
        </template>
        <Col :span="24">
          <FormItem label="备注" name="remarks" v-bind="{ labelCol: { span: 3 }, wrapperCol: { span: 21 } }">
            <Textarea
              v-model:value="detailForm.remarks"
              :rows="3"
              :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
              class="w-full"
            />
          </FormItem>
        </Col>
      </Row>

      <BasicCaption content="上游企业信息" />
      <div>
        <GridSupplier>
          <template #toolbarTools v-if="!detailForm.id || detailForm.status === nonEditStatus">
            <Button
              class="mr-2"
              type="primary"
              :disabled="isAddSupplierDisabled"
              @click="() => selectCompany(gridApiSupplier)"
            >
              选择企业
            </Button>
            <Button class="mr-2" danger @click="() => removeAccount(gridApiSupplier)">删行</Button>
          </template>
          <template #companyName="{ row }">
            {{ row.companyName }}
          </template>
          <template #companyCode="{ row }">
            {{ row.companyCode }}
          </template>
        </GridSupplier>
      </div>
      <BasicCaption content="下游企业信息" />
      <div>
        <GridPurchaser>
          <template #toolbarTools v-if="!detailForm.id || detailForm.status === nonEditStatus">
            <Button
              class="mr-2"
              type="primary"
              :disabled="isAddPurchaserDisabled"
              @click="() => selectCompany(gridApiPurchaser)"
            >
              选择企业
            </Button>
            <Button class="mr-2" danger @click="() => removeAccount(gridApiPurchaser)">删行</Button>
          </template>
          <template #companyName="{ row }">
            {{ row.companyName }}
          </template>
          <template #companyCode="{ row }">
            {{ row.companyCode }}
          </template>
        </GridPurchaser>
      </div>
      <template v-if="detailForm.businessStructure !== 'GENERAL'">
        <BasicCaption content="授信企业信息" />
        <div>
          <GridCredit ref="gridCreditRef">
            <!--<template #toolbarTools v-if="!detailForm.id || detailForm.status === nonEditStatus">-->
            <!--  <Button-->
            <!--    class="mr-2"-->
            <!--    type="primary"-->
            <!--    :disabled="isCreditGridLocked"-->
            <!--    @click="() => selectCompany(gridApiCredit)"-->
            <!--  >-->
            <!--    选择企业-->
            <!--  </Button>-->
            <!--  <Button class="mr-2" danger :disabled="isCreditGridLocked" @click="() => removeAccount(gridApiCredit)">-->
            <!--    删行-->
            <!--  </Button>-->
            <!--</template>-->
            <template #companyName="{ row }">
              {{ row.companyName }}
            </template>
            <template #companyCode="{ row }">
              {{ row.companyCode }}
            </template>
            <template #edit_sub_limit_amount="{ row }">
              <InputNumber
                v-model:value="row.subLimitAmount"
                placeholder="请输入企业额度上限"
                class="w-full"
                :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
              />
            </template>
            <template #occupyLimit="{ row }">
              <Select
                v-model:value="row.occupyLimit"
                placeholder="是否占用企业总额度"
                :options="getDictList('baseBooleanType')"
                :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
                class="w-full"
              />
            </template>
          </GridCredit>
        </div>
      </template>

      <BasicCaption content="增信措施" />
      <div class="mt-5">
        <FormItem
          label="增信措施描述"
          name="creditEnhancementDesc"
          v-bind="{ labelCol: { span: 3 }, wrapperCol: { span: 21 } }"
        >
          <Textarea
            v-model:value="detailForm.creditEnhancementDesc"
            :rows="3"
            :disabled="!!detailForm.id && detailForm.status !== nonEditStatus"
          />
        </FormItem>
      </div>

      <BaseAttachmentList
        border="inner"
        v-model="detailForm.attachmentList"
        :business-id="detailForm.id"
        business-type="SCM_PROJECT"
        :edit-mode="!detailForm.id || detailForm.status === nonEditStatus"
      />
    </Form>

    <Modal
      v-model:open="companySelectorModal.visible"
      title="选择企业"
      @ok="handleCompanySelectOk"
      @cancel="handleCompanySelectCancel"
      width="600px"
    >
      <Form style="margin-top: 20px">
        <FormItem label="选择企业">
          <Select
            v-model:value="companySelectorModal.company.companyCode"
            placeholder="请选择企业"
            :options="companyOptions"
            :field-names="{ label: 'companyName', value: 'companyCode' }"
            show-search
            :filter-option="(input: string, option: any) => option.companyName.includes(input)"
            @change="handleCompanyChange"
            style="width: 100%"
          />
        </FormItem>
        <FormItem label="统一社会信用代码">
          <div v-if="companySelectorModal.company.companyCode">
            {{ companySelectorModal.company.companyCode }}
          </div>
          <div v-else class="text-gray-400">请选择企业</div>
        </FormItem>
      </Form>
    </Modal>
  </BasicPopup>
  <WorkflowPreviewModal v-if="pageType === 'edit'" />
</template>

<style scoped>
.avatar-uploader > .ant-upload {
  width: 128px;
  height: 128px;
}

.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}

.ant-input-number,
.ant-picker {
  width: 100% !important;
}

.ant-picker-focused {
  width: 100% !important;
}

/* 合作费率表格样式 */
.service-fee-rate-table {
  border-collapse: collapse;
}

.service-fee-rate-table .ant-table-thead > tr > th {
  border-bottom: 1px solid #d9d9d9;
  background-color: #fafafa;
  padding: 8px 4px;
  text-align: center;
}

.service-fee-rate-table .ant-table-tbody > tr > td {
  border-bottom: 1px solid #d9d9d9;
  padding: 8px 4px;
  text-align: center;
}

.service-fee-rate-table .ant-table-tbody > tr:last-child > td {
  border-bottom: 1px solid #d9d9d9;
}

/* 表头输入框样式 */
.service-fee-rate-table .ant-table-thead .ant-input-number {
  border: 1px solid #d9d9d9;
}

.service-fee-rate-table .ant-table-thead .ant-input-number-input {
  text-align: center;
}
</style>
