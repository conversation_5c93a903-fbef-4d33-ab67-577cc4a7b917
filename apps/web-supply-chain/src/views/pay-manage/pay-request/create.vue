<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { GridApi, VxeTableGridOptions } from '#/adapter/vxe-table';

import { computed, reactive, ref } from 'vue';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defaultsDeep } from '@vben/utils';

import { Button, Col, DatePicker, Form, FormItem, Input, message, Row, Select, Textarea, Progress, Switch } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import DetailModal from "./detailModal.vue";
import BusinessModal from "./businessModal.vue"
import {evaluateAddApi, type EvaluateBaseInfo} from "#/api";
// import { type AddInbound, type InboundReceiptItemBOs, type QueryGoodsRequest, addInboundApi, editInboundApi, inboundDetailApi, getPurchaseGoodsApi } from '#/api';

const emit = defineEmits(['register', 'ok']);

// 字典选项
const businessStructureOptions = ref([
  { label: '先采后销', value: '1' },
  { label: '先销后采', value: '2' },
]);

const projectModelOptions = ref([
  { label: '产业1', value: '1' },
  { label: '产业2', value: '2' },
  { label: '产业3', value: '3' },
  { label: '产业4', value: '4' },
  { label: '产业5', value: '5' },
]);

const executorCompanyOpations = ref([
  { label: 'aa', value: '1' },
  { label: 'bb', value: '2' },
]);

const executorCompanyOptions = ref([
  { label: '上海负责人', value: '1' },
  { label: '北京负责人', value: '2' },
]);

const { getDictList } = useDictStore();

// 默认数据
const defaultForm: Partial<AddInbound> = {
  id: undefined,
  createBy: 0,
  createTime: '',
  updateBy: 0,
  updateTime: '',
  version: 0,
  inboundReceiptCode: '',
  receiptDate:"",
  projectId: 0,
  projectName:  undefined,
  projectCode: '',
  warehouseId: 0,
  warehouseCode: '',
  warehouseName: '',
  customerCompanyCode: '',
  customerCompanyName: '',
  sourceDocumentType: '',
  deliveryReceiptId: 0,
  deliveryReceiptDisplay: '',
  amountWithTax: 0,
  invoicedAmountWithTax: 0,
  status: '',
  approvalStatus: '',
  invoiceStatus: '',
  remarks: '',
  isSnManaged: 0,
  inboundReceiptSourceRelBOS: [{
    sourceDocumentId: 123,
    sourceDocumentDisplay: '345',
    sourceDocumentType: '555'
  },
  ],
  inboundReceiptItemBOs: [],
};

let detailForm = reactive<Partial<AddInbound>>(defaultsDeep(defaultForm));

const colSpan = { md: 12, sm: 24 };

const rules: Record<string, Rule[]> = {
  // inboundReceiptCode: [{ required: true, message: '入库单编号', trigger: 'change' }],
  // projectName: [{ required: true, message: '所属项目名称', trigger: 'change' }],
  projectCode: [{ required: true, message: '所属项目编号', trigger: 'change' }],
  // sourceDocumentType: [{ required: true, message: '关联单据类型', trigger: 'change' }],
  // deliveryReceiptId: [{ required: true, message: '关联单据', trigger: 'change' }],
  // receiptDate: [{ required: true, message: '入库日期', trigger: 'change' }],
  // customerCompanyName: [{ required: true, message: '上/下游企业', trigger: 'change' }],
  // warehouseName: [{ required: true, message: '仓库名称', trigger: 'change' }],
};

const title = computed(() => {
  return detailForm.id ? '编辑项目' : '新增项目';
});

const init = async (data: any) => {
  if (data.id) {
    const res = await inboundDetailApi(data.id); // 调用真实 API

    // 深度复制确保响应性
    Object.keys(res).forEach(key => {
      detailForm[key] = res[key];
    });

    // 强制校验并转换inboundReceiptItemBOs字段
    if (!Array.isArray(res.inboundReceiptItemBOs) || res.inboundReceiptItemBOs === null) {
      detailForm.inboundReceiptItemBOs = [];
    } else {
      // 创建全新数组实例确保响应性
      detailForm.inboundReceiptItemBOs = [...res.inboundReceiptItemBOs];
    }

    // 强制刷新表格
    if (gridApiLocation?.grid) {
      gridApiLocation.grid.reloadData(detailForm.inboundReceiptItemBOs);
    }
  } else {
    Object.assign(detailForm, defaultsDeep(defaultForm));
    detailForm.inboundReceiptItemBOs = defaultForm.inboundReceiptItemBOs ? [...defaultForm.inboundReceiptItemBOs] : [];
  }
};

const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);

const formRef = ref();
const save = async () => {
  try {
    await formRef.value.validate();

    changeOkLoading(true);
    // 显式类型断言确保类型正确
    const submitData = detailForm as Required<AddInbound>;

    // 根据是否存在id判断是新增还是编辑
    const res = detailForm.id
        ? await editInboundApi(submitData)
        : await addInboundApi(submitData);

    // 使用国际化提示保存成功
    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } catch (error) {
    message.error('保存失败，请检查网络或输入内容');
    console.error('新增仓库失败:', error);
    changeOkLoading(false); // 防止 loading 卡住
  } finally {
    changeOkLoading(false);
  }
};

const labelCol = { style: { width: '150px' } };

// 在表格配置中添加key属性确保强制刷新
const gridLocation: VxeTableGridOptions = {
  data: detailForm.inboundReceiptItemBOs,
  props: {
    key: computed(() => detailForm.id || 'new') // 添加key属性
  },
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    {
      field: 'quantity',
      title: '源单单据编号',
      editRender: {},
      slots: { edit: 'edit_quantity' },
      minWidth: '150px',
    }, // 自己填，得小于未入库
    {
      field: 'sourceDocumentItemNumber',
      title: '源单单据类型',
      editRender: {},
      slots: { edit: 'edit_source_document_item_number' },
      minWidth: '150px',
    },
    {
      field: 'sourceDocumentDisplay',
      title: '应付总金额',
      editRender: {},
      slots: { edit: 'edit_source_document_display' },
      minWidth: '150px',
    },
    { field: 'serialNumbers', title: '本次申请付款金额', editRender: {}, slots: { edit: 'edit_serial_numbers' }, minWidth: '150px' },
    { field: 'remarks', title: '备注', editRender: {}, slots: { edit: 'edit_remarks' }, minWidth: '200px' },
  ],
  editConfig: {
    trigger: 'click',
    mode: 'row',
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar_location_tools',
    },
  },
};

const detailModal = ref();
// 数据明细
const dataDetails = () => {
  detailModal.value.openModal()
}

const businessModal = ref();
// 选择关联单据
const selectBusinessDocument = () => {
  businessModal.value.openModal()
}

// 新增行
const addLocationRow = async (gridApi: GridApi) => {
  const newRecord = {
    id: undefined,
    createBy: 0,
    createTime: '',
    updateBy: 0,
    updateTime: '',
    version: 0,
    inboundReceiptId: 0,
    sourceDocumentItemNumber: '0',
    productName: '',
    productCode: '',
    productAlias: '',
    measureUnit: '',
    specifications: '',
    originName: '',
    brandName: '',
    quantity: 0,
    warehouseId: 0,
    serialNumbers: '',
    sourceDocumentType: '',
    sourceDocumentId: 0,
    sourceDocumentDisplay: '',
    sourceDocumentItemId: 0,
    sourceDocumentItemDisplay: '0',
    remarks: '',
  };
  const $grid = gridApi.grid;
  if ($grid) {
    try {
      // 插入新行前强制同步数据源
      if (!Array.isArray(detailForm.inboundReceiptItemBOs)) {
        detailForm.inboundReceiptItemBOs = [];
      }

      // 使用扩展运算符确保数组响应性
      detailForm.inboundReceiptItemBOs = [...detailForm.inboundReceiptItemBOs, newRecord];

      // 替换为官方支持的刷新方式
      if (typeof $grid.reloadData === 'function') {
        $grid.reloadData(detailForm.inboundReceiptItemBOs);
      }
    } catch (error) {
      console.error('插入行失败:', error);
      message.error('添加行失败，请检查字段类型或网络状态');
    }
  }
};

// 删除行
const removeLocationRow = async (gridApi: GridApi) => {
  const $grid = gridApi.grid;
  if ($grid) {
    const selectRecords = $grid.getCheckboxRecords();
    if (!Array.isArray(selectRecords)) {
      message.warning('请选择要删除的数据');
      return;
    }

    if (selectRecords.length > 0) {
      const { deleteInboundReceiptItemBOs } = detailForm;

      // 确保 deleteInboundReceiptItemBOs 是数组
      if (!Array.isArray(deleteInboundReceiptItemBOs)) {
        detailForm.deleteInboundReceiptItemBOs = [...selectRecords];
      } else {
        detailForm.deleteInboundReceiptItemBOs = [...deleteInboundReceiptItemBOs, ...selectRecords];
      }

      $grid.remove(selectRecords);
    } else {
      message.warning('请选择要删除的数据');
    }
  }
};

const projectCodeOptions = [
  {
    value: '26',
    label: '项目1'
  },
  {
    value: '2',
    label: '项目2'
  },
  {
    value: '3',
    label: '项目3'
  },
  {
    value: '4',
    label: '项目4'
  }
]

// 创建响应式的查询参数
const purchaseGoodsParams = reactive<QueryGoodsRequest>({
  projectId: undefined,
  ids: []
});

const getPurchaseGoodsFn = async (value: QueryGoodsRequest) => {
  if (value.projectId) {
    try {
      const res = await getPurchaseGoodsApi(purchaseGoodsParams);
      if (res) {
        detailForm.inboundReceiptItemBOs = [...res];

        // 强制刷新表格
        if (gridApiLocation?.grid) {
          await gridApiLocation.grid.reloadData(detailForm.inboundReceiptItemBOs);
        }
      }
    } catch (error) {
      console.error('获取商品信息失败:', error);
      message.error('获取商品信息失败');
    }
  } else {
    // 如果没有选择任何项目，清空商品列表
    detailForm.inboundReceiptItemBOs = [];
    if (gridApiLocation?.grid) {
      await gridApiLocation.grid.reloadData([]);
    }
  }
}

const handleChangeProjectName = (selectedValues: string[]) => {
  // 清空之前的选择
  purchaseGoodsParams.ids = selectedValues;
  getPurchaseGoodsFn(purchaseGoodsParams)
};

const handleChangeProjectCode = (projectId: number) => {
  // 更新查询参数
  purchaseGoodsParams.projectId = projectId;
  getPurchaseGoodsFn(purchaseGoodsParams)
};

// 注册表格
const [GridLocation, gridApiLocation] = useVbenVxeGrid({
  gridOptions: gridLocation,
  watch: {
    data: {
      handler: (newVal) => {
        if (gridApiLocation?.grid && newVal) {
          setTimeout(() => {
            gridApiLocation.grid.reloadData(newVal);
          }, 0);
        }
      },
      deep: true,
      immediate: true
    }
  }
});
const checked = ref(false);
const detailModalConfirm = async (data: EvaluateBaseInfo) => {
  const res = await evaluateAddApi(data);
  if (res) {
    await gridApi.reload();
    message.success($t('base.resSuccess'));
  }
}

const businessModalConfirm = async (data: EvaluateBaseInfo) => {
  const res = await evaluateAddApi(data);
  if (res) {
    await gridApi.reload();
    message.success($t('base.resSuccess'));
  }
}
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn :title="title" @register="registerPopup" @ok="save">
    <Form
        ref="formRef"
        :colon="false"
        :model="detailForm"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="{ span: 20 }"
        class="px-8"
    >
      <!-- 基本信息 -->
      <BasicCaption content="基本信息" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="付款申请单编号" name="inboundReceiptCode">
            <Input v-model:value="detailForm.inboundReceiptCode" />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="终端企业" name="inboundReceiptCode">
            <Input v-model:value="detailForm.inboundReceiptCode" />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="所属项目名称" name="projectName">
            <Select
                v-model:value="detailForm.projectName"
                mode="multiple"
                :options="projectModelOptions"
                @change="handleChangeProjectName"
                placeholder="请选择所属项目名称"
            />
          </FormItem>
        </Col>

        <!-- 所属项目编号 -->
        <Col v-bind="colSpan">
          <FormItem label="所属项目编号" name="projectCode">
            <Select
                v-model:value="detailForm.projectCode"
                :options="projectCodeOptions"
                @change="handleChangeProjectCode"
                placeholder="请选择所属项目编号"
            />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="付款方企业" name="deliveryReceiptDisplay">
            <Select v-model:value="detailForm.deliveryReceiptDisplay" :options="businessStructureOptions" />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="收款方企业" name="deliveryReceiptDisplay">
            <Select v-model:value="detailForm.deliveryReceiptDisplay" :options="businessStructureOptions" />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="付款方社会统一信用代码" name="sourceDocumentType">
            <Input v-model:value="detailForm.sourceDocumentType" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="收款方社会统一信用代码" name="sourceDocumentType">
            <Input v-model:value="detailForm.sourceDocumentType" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="付款方银行账号" name="sourceDocumentType">
            <Input v-model:value="detailForm.sourceDocumentType" />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="收款方银行账号" name="sourceDocumentType">
            <Input v-model:value="detailForm.sourceDocumentType" />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="付款方开户行" name="sourceDocumentType">
            <Input v-model:value="detailForm.sourceDocumentType" />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="收款方开户行" name="sourceDocumentType">
            <Input v-model:value="detailForm.sourceDocumentType" />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="计划付款日期" name="receiptDate">
            <DatePicker
                v-model:value="detailForm.receiptDate"
                value-format="YYYY-MM-DD"
            />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="计划收款日期" name="receiptDate">
            <DatePicker
                v-model:value="detailForm.receiptDate"
                value-format="YYYY-MM-DD"
            />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="备注" name="remarks">
            <Textarea v-model:value="detailForm.remarks" :rows="3" />
          </FormItem>
        </Col>
      </Row>

      <!-- 付款申请信息 -->
      <BasicCaption content="付款申请信息" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="关联单据类型" name="deliveryReceiptDisplay">
            <Select v-model:value="detailForm.deliveryReceiptDisplay" :options="businessStructureOptions" />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="计划付款金额" name="inboundReceiptCode">
            <Input v-model:value="detailForm.inboundReceiptCode" disabled />
          </FormItem>
        </Col>
      </Row>

      <!-- 额度信息 -->
      <BasicCaption content="额度信息" />
      <Row class="mt-5">
        <Col v-bind="colSpan" class="flex justify-around">
          <div>
            <FormItem label="项目总额度" name="deliveryReceiptDisplay">
              {{ 500000 }}
            </FormItem>
            <FormItem label="项目在途额度" name="deliveryReceiptDisplay">
              {{ 600000 }}
            </FormItem>
            <FormItem label="付款后在途额度" name="deliveryReceiptDisplay">
              {{ 700000 }}
            </FormItem>
          </div>
         <div>
           <div>
             额度使用占比
           </div>
           <Progress v-if="checked" stroke-linecap="square" :percent="75" type="circle"/>
           <Progress v-if="!checked" stroke-linecap="square" :percent="60" type="circle"/>
           <div>
             付款前  <Switch v-model:checked="checked" />  付款后
           </div>
         </div>
        </Col>

        <Col v-bind="colSpan" class="flex justify-around">
          <div>
            <FormItem label="企业总额度" name="deliveryReceiptDisplay">
              {{ 500000 }}
            </FormItem>
            <FormItem label="企业在途额度" name="deliveryReceiptDisplay">
              {{ 600000 }}
            </FormItem>
            <FormItem label="付款后在途额度" name="deliveryReceiptDisplay">
              {{ 700000 }}
            </FormItem>
          </div>
          <div>
            <div>
              额度使用占比
            </div>
            <Progress v-if="checked" stroke-linecap="square" :percent="75" type="circle"/>
            <Progress v-if="!checked" stroke-linecap="square" :percent="60" type="circle"/>
            <div>
              付款前  <Switch v-model:checked="checked" />  付款后
            </div>
          </div>
        </Col>
      </Row>

      <!-- 货款及费用信息 -->
      <BasicCaption content="货款及费用信息" />
      <div>
        <GridLocation>
          <template #toolbar_location_tools>
            <Button class="mr-2" type="primary" @click="dataDetails">数据明细</Button>
            <Button class="mr-2" type="primary" @click="selectBusinessDocument">选择业务单据</Button>
            <Button class="mr-2" type="primary" @click="() => addLocationRow(gridApiLocation)">增行</Button>
            <Button class="mr-2" danger @click="() => removeLocationRow(gridApiLocation)">删行</Button>
          </template>

          <template #edit_quantity="{ row }">
            <Input v-model:value="row.quantity" placeholder="源单单据编号" disabled/>
          </template>

          <template #edit_source_document_item_number="{ row }">
            <Input v-model:value="row.sourceDocumentItemNumber" placeholder="源单单据类型" disabled/>
          </template>

          <template #edit_source_document_display="{ row }">
            <Input v-model:value="row.sourceDocumentDisplay" placeholder="应付总金额" disabled/>
          </template>

          <template #edit_serial_numbers="{ row }">
            <Input v-model:value="row.serialNumbers" placeholder="本次申请付款金额" />
          </template>

          <template #edit_remarks="{ row }">
            <Input v-model:value="row.remarks" placeholder="请输入备注" />
          </template>
        </GridLocation>
      </div>
    </Form>
    <DetailModal ref="detailModal" @confirm="detailModalConfirm" />
    <BusinessModal ref="businessModal" @confirm="businessModalConfirm" />
  </BasicPopup>
</template>

<style scoped>
:where(.css-dev-only-do-not-override-1gaak89).ant-picker {
  width: 100%;
}
</style>
