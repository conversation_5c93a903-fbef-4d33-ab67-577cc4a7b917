<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { onMounted, ref } from 'vue';

import { useModalUrl } from '@vben/base-ui';
import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Modal as AntdModal, Button, message, Space, TypographyLink } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { delTransportApi, getCompanyApi, getTransportPageApi, cancelTransportApi } from '#/api';

import Create from './create.vue';
import Detail from './detail.vue';

const { getDictList } = useDictStore();

const sortKey = ref<string>('create_time');
const companyOptions = ref([]);
// 获取企业列表
const getCompanyList = async () => {
  const res = await getCompanyApi();
  Object.assign(companyOptions.value, res);
};

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'docCode',
      label: '运输指令单编号',
    },
    {
      component: 'Input',
      fieldName: 'projectCode',
      label: '所属项目编号',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '所属项目名称',
    },
    {
      component: 'Select',
      fieldName: 'entrustedCompanyCode',
      label: '受托企业',
      componentProps: {
        options: companyOptions.value,
        fieldNames: { label: 'companyName', value: 'companyCode' },
        allowClear: true,
        showSearch: true,
        optionFilterProp: 'companyName',
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '状态',
      componentProps: {
        options: getDictList('BUS_STATUS'),
      },
    },
    {
      component: 'Select',
      fieldName: 'approvalStatus',
      label: '审批状态',
      componentProps: {
        options: getDictList('REVIEW_STATUS'),
      },
    },
    {
      component: 'DateRangePicker',
      fieldName: 'entrustedDate',
      label: '委托日期',
    },
  ],
  fieldMappingTime: [
    ['entrustedDate', ['entrustedStartDate', 'entrustedEndDate'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
  ],
  showCollapseButton: true,
  submitOnEnter: true,
});

const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'docCode', title: '运输指令单编号' },
    { field: 'projectCode', title: '项目编号' },
    { field: 'projectName', title: '项目名称' },
    {
      field: 'entrustedCompanyCode',
      title: '受托企业',
      formatter: ({ cellValue }) => {
        if (!cellValue) return '';
        // 返回字符串，按逗号分割
        if (typeof cellValue === 'string') {
          const values = cellValue.split(',').filter((item) => item.trim() !== '');
          const dictList = companyOptions.value;

          return values
            .map((value) => {
              const dictItem = dictList.find((item) => item.companyCode === value);
              return dictItem ? dictItem.companyName : value;
            })
            .join(', ');
        }
        return cellValue;
      },
    },
    { field: 'entrustedDate', title: '委托日期', formatter: 'formatDate' },
    {
      field: 'status',
      title: '状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'BUS_STATUS',
        },
      },
    },
    {
      field: 'approvalStatus',
      title: '审批状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'REVIEW_STATUS',
        },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getTransportPageApi({
          current: page.currentPage,
          size: page.pageSize,
          descs: sortKey.value,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

// 创建
const add = () => {
  openFormPopup(true, { pageType: 'edit' });
};

// 编辑
const handleEdit = (row: TransOrderBaseInfo) => {
  openFormPopup(true, { ...row, pageType: 'edit' });
};

// 查看
const handleDetail = (row: TransOrderBaseInfo) => {
  openDetailPopup(true, { ...row, pageType: 'detail' });
};

// 删除
const del = (row: TransOrderBaseInfo) => {
  AntdModal.confirm({
    title: $t('base.confirmDelTitle'),
    content: $t('base.confirmDelContent'),
    async onOk() {
      await delTransportApi(row.id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};

const cancel = (row: TransOrderBaseInfo) => {
  AntdModal.confirm({
    title: $t('base.confirmCancelTitle'),
    content: $t('base.confirmCancelContent'),
    async onOk() {
      await cancelTransportApi(row.id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};

const access = (row: { id: number }) => {
  openFormPopup(true, { ...row, pageType: 'edit' });
};
const viewDetail = (row: { id: number }) => {
  openDetailPopup(true, { ...row, pageType: 'detail' });
};
const audit = (row: { id: number }) => {
  openDetailPopup(true, { ...row, pageType: 'audit' });
};

useModalUrl({
  handlers: {
    // 编辑弹层
    edit: (params) => {
      const data: AccessInfo = {
        id: params.id,
      };
      access(data);
    },

    // 详情弹层
    detail: (params) => {
      const data: AccessInfo = {
        id: params.id,
      };
      viewDetail(data);
    },

    // 审核弹层
    audit: (params) => {
      const data: AccessInfo = {
        id: params.id,
        pageType: 'audit',
      };
      audit(data);
    },
  },
});

const editSuccess = () => {
  gridApi.formApi.submitForm();
};

onMounted(async () => {
  await getCompanyList();
});
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button class="mr-2" type="primary" @click="add">
          <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />
          {{ $t('base.add') }}
        </Button>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink
            v-if="row.status === 'DRAFTING' || row.approvalStatus === 'REJECTED'"
            @click="handleEdit(row)"
          >
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink @click="handleDetail(row)">
            {{ $t('base.detail') }}
          </TypographyLink>
          <TypographyLink v-if="row.status === 'DRAFTING'" type="danger" @click="del(row)">
            {{ $t('base.del') }}
          </TypographyLink>
          <TypographyLink
            v-if="row.status === 'DRAFTING' || row.approvalStatus === 'REJECTED'"
            type="danger"
            @click="cancel(row)"
          >
            {{ $t('base.invalidate') }}
          </TypographyLink>
        </Space>
      </template>
    </Grid>
    <Create @register="registerForm" @ok="editSuccess" />
    <Detail @register="registerDetail" @ok="editSuccess" />
  </Page>
</template>

<style></style>
