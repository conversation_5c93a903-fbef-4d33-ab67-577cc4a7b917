<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import { onMounted, ref } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import { isEmpty } from 'lodash-es';

import { getStatisticsProjectFactoryApi } from '#/api';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

const getStatistics = async () => {
  const res = await getStatisticsProjectFactoryApi();
  if (!isEmpty(res)) {
    res.forEach((item) => {
      item.value = item.tenThousandAmount;
      item.name = item.factoringType;
    });
  }
  return res || [];
};

onMounted(async () => {
  const infoDetail = await getStatistics();
  renderEcharts({
    legend: {
      bottom: '10',
      left: 'center',
    },
    series: [
      {
        animationEasing: 'exponentialInOut',
        animationType: 'scale',
        avoidLabelOverlap: false,
        color: ['#5ab1ef', '#b6a2de', '#67e0e3', '#2ec7c9'],
        data: infoDetail,
        emphasis: {
          label: {
            fontSize: '12',
            fontWeight: 'bold',
            show: true,
          },
        },
        label: {
          position: 'center',
          show: false,
        },
        labelLine: {
          show: false,
        },
        name: '访问来源',
        radius: ['40%', '64%'],
        center: ['50%', '40%'],
        type: 'pie',
      },
    ],
    tooltip: {
      trigger: 'item',
    },
  });
});
</script>

<template>
  <EchartsUI ref="chartRef" />
</template>
