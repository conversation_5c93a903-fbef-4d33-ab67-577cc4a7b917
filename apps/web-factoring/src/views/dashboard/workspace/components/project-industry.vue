<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import { onMounted, ref } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import { isEmpty } from 'lodash-es';

import { getStatisticsProjectIndustryApi } from '#/api';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);
const colors = [
  '#4895ef',
  '#f7ba1e',
  '#f56a6a',
  '#722ed1',
  '#13c2c2',
  '#fa8c16',
  '#52c41a',
  '#86909c',
  '#ff4d4f',
  '#bf73f6',
  '#1890ff',
  '#faad14',
  '#73d13d',
  '#ff7a45',
  '#2f54eb',
  '#e53935',
  '#6f42c1',
  '#00b42a',
  '#34c759',
  '#6495ed',
];
const getStatistics = async () => {
  const res = await getStatisticsProjectIndustryApi();
  const data = {
    name: [],
    value: [],
  };
  if (!isEmpty(res)) {
    res.forEach((item, index) => {
      data.name.push(item.industry);
      item.value = Number(item.ratio);
      item.name = item.industry;
      item.itemStyle = {
        color: colors[index],
      };
      data.value.push(item);
    });
  }
  return data;
};

onMounted(async () => {
  const infoDetail = await getStatistics();
  renderEcharts({
    angleAxis: {
      type: 'category',
      data: infoDetail.name,
    },
    radiusAxis: {
      min: 0,
      max: 100,
      interval: 20,
    },
    polar: {
      center: ['50%', '35%'],
      radius: '60%', // 缩小极坐标整体范围
    },
    tooltip: {
      confine: true,
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c} ({d}%)',
    },
    legend: {
      show: true,
      top: '70%',
      data: infoDetail.name,
    },
    calculable: true,
    series: [
      {
        stack: 'a',
        type: 'pie',
        radius: '55%',
        center: ['50%', '35%'],
        roseType: 'area',
        label: {
          normal: {
            show: false,
          },
          emphasis: {
            show: false,
          },
        },
        data: infoDetail.value,
      },
    ],
  });
});
</script>

<template>
  <EchartsUI ref="chartRef" />
</template>
