<script setup lang="ts">
import type { UploadChangeParam, UploadFile } from 'ant-design-vue';
import type { UploadListType } from 'ant-design-vue/es/upload/interface';
import type { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface';

import type { PropType } from 'vue';

import { nextTick, ref, watch } from 'vue';

import { LoadingOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { Button, Image, Upload } from 'ant-design-vue';

const props = defineProps({
  listType: { type: String as PropType<UploadListType>, default: 'picture-card' },
  maxCount: { type: Number, default: 1 },
  showUploadList: { type: Boolean, default: false },
  btnText: { type: String, default: '上传' },
  uploadApi: { type: Function, required: true },
  previewApi: { type: Function, required: true },
});
const emit = defineEmits(['uploadSuccess']);
const fileId = defineModel({ type: Number });
const fileLink = defineModel('link', { type: String });
const fileInfo = defineModel('info', { type: Object });
const fileIdList = defineModel('idList', { type: Array });
const fileList = ref<UploadFile[]>([]);
const loading = ref(false);

// 防循环机制
const isSyncing = ref(false);

const syncFileIdList = () => {
  if (isSyncing.value) return;

  const idList: number[] = [];
  fileList.value.forEach((file: UploadFile<any> & { attachId?: number }) => {
    if (file.attachId) {
      idList.push(file.attachId);
    }
  });

  // 只有当 idList 真的发生变化时才更新
  const currentIds = (fileIdList.value as number[]) || [];
  if (JSON.stringify(idList.sort()) !== JSON.stringify(currentIds.sort())) {
    isSyncing.value = true;
    fileIdList.value = idList;
    nextTick(() => {
      isSyncing.value = false;
    });
  }
};

// 根据 fileIdList 生成 fileList
const syncFileListFromIds = async () => {
  if (isSyncing.value) return;

  const idList = (fileIdList.value as number[]) || [];

  // 如果 fileIdList 为空，清空 fileList
  if (idList.length === 0) {
    if (fileList.value.length > 0) {
      isSyncing.value = true;
      fileList.value = [];
      await nextTick(() => {
        isSyncing.value = false;
      });
    }
    return;
  }

  isSyncing.value = true;
  try {
    const newFileList: (UploadFile<any> & { attachId?: number })[] = [];

    for (const id of idList) {
      if (id) {
        try {
          const url = await props.previewApi({ id });

          // 从 URL 中提取文件名
          let fileName = `file-${id}`;
          try {
            const urlPath = new URL(url).pathname;
            const extractedName = urlPath.split('/').pop();
            if (extractedName && extractedName.includes('.')) {
              fileName = decodeURIComponent(extractedName);
            }
          } catch {
            // 如果 URL 解析失败，尝试简单的字符串分割
            const parts = url.split('/');
            const lastPart = parts[parts.length - 1];
            if (lastPart && lastPart.includes('.')) {
              fileName = decodeURIComponent(lastPart);
            }
          }

          newFileList.push({
            uid: `-${id}`,
            name: fileName,
            status: 'done',
            url,
            attachId: id,
          });
        } catch (error) {
          console.warn(`Failed to get preview for file ${id}:`, error);
        }
      }
    }

    fileList.value = newFileList;
  } finally {
    await nextTick(() => {
      isSyncing.value = false;
    });
  }
};
// 监听 fileList 变化，自动同步到 fileIdList
watch(
  fileList,
  () => {
    // console.log('fileList changed, isSyncing:', isSyncing.value);
    syncFileIdList();
  },
  { deep: true },
);

// 监听 fileIdList 变化，自动生成对应的 fileList
watch(
  fileIdList,
  () => {
    // console.log('fileIdList changed, isSyncing:', isSyncing.value);
    syncFileListFromIds();
  },
  {
    immediate: true,
    deep: true,
  },
);

watch(
  fileId,
  async (value) => {
    if (value && !fileLink.value) {
      fileLink.value = await props.previewApi({ id: value });
    }
  },
  { immediate: true },
);
const handleUploadChange = (info: UploadChangeParam) => {
  let resFileList = [...info.fileList];
  resFileList = resFileList.map((file: UploadFile<any> & { attachId?: number }) => {
    if (file.response) {
      file.url = file.response.url;
      file.attachId = file.response.attachId;
    }
    return file;
  });

  // 直接更新，让监听器处理同步
  fileList.value = resFileList;
  loading.value = info.file.status === 'uploading';
  if (info.file.status === 'done') {
    if (props.maxCount === 1) {
      fileInfo.value = info.file.response;
      fileId.value = info.file.response.attachId;
      fileLink.value = info.file.response.link;
    }
    emit('uploadSuccess', info.file.response);
  }
};
const uploadFile = ({
  data = {},
  file,
  filename = 'file',
  headers,
  onError,
  onProgress,
  onSuccess,
  withCredentials,
}: UploadRequestOption) => {
  const formData = { ...data };
  formData[filename] = file;
  // 使用框架上传方法已实现 FormData 不需要重复转换
  // const formData = new FormData();
  // if (data) {
  //   Object.keys(data).forEach((key) => {
  //     formData.append(key, data[key] as string);
  //   });
  // }
  // formData.append(filename, file);
  props
    .uploadApi(formData, {
      withCredentials,
      headers,
      onUploadProgress: ({ total, loaded }: { loaded: number; total: number }) => {
        if (onProgress) {
          onProgress({ percent: Number(Math.round((loaded / total) * 100).toFixed(2)) });
        }
      },
    })
    .then((response: unknown) => {
      if (onSuccess) {
        onSuccess(response);
      }
    })
    .catch(onError);
  return {
    abort() {
      console.warn('upload progress is aborted.');
    },
  };
};
</script>

<template>
  <Upload
    :file-list="fileList"
    :custom-request="uploadFile"
    :show-upload-list="showUploadList"
    :max-count="maxCount"
    :list-type="listType"
    v-bind="$attrs"
    @change="handleUploadChange"
  >
    <slot name="default">
      <template v-if="listType !== 'text'">
        <Image v-if="fileLink && !showUploadList" :src="fileLink" alt="" :preview="false" />
        <div v-else>
          <LoadingOutlined v-if="loading" />
          <PlusOutlined v-else />
          <div>{{ btnText }}</div>
        </div>
      </template>
      <Button v-else type="primary">{{ btnText }}</Button>
    </slot>
  </Upload>
</template>

<style scoped></style>
